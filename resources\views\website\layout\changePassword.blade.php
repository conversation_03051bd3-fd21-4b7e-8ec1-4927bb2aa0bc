<div class="modal fade login change" id="change" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content p-4">
            <form id="password_reset_form">
                @csrf
                <fieldset>
                    <div class="modal-header b-none">
                        <h4 class="modal-title mx-auto">{{ translate('user_account_setting.change_password') }}</h4>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center">
                        <div id="password-alert-box">
                            <div class="alert alert-danger" role="alert">
                                <p class="m-0"></p>
                            </div>
                        </div>
                        <div class="form-outline mb-4">
                            <input type="password" class="form-control" name="old_password"
                                placeholder="{{ translate('user_account_setting.enter_old_password') }}"
                                id="old_passrword" autocomplete="current-password" />
                            <i class="fas fa-eye eye_icon" id="toggle-pass"></i>
                        </div>
                        <div class="form-outline mb-4">
                            <input type="password" class="form-control" name="new_password"
                                placeholder="{{ translate('user_account_setting.enter_new_password') }}"
                                id="new_passrword" autocomplete="new-password" />
                            <i class="fas fa-eye eye_icon" id="toggle-pass1"></i>
                        </div>
                        <div class="form-outline mb-4">
                            <input type="password" class="form-control" name="confirm_password"
                                placeholder="{{ translate('user_account_setting.confirm_new_password') }}"
                                id="confirm_new_passrword" autocomplete="re-enter-password" />
                            <i class="fas fa-eye eye_icon" id="toggle-pass2"></i>
                        </div>
                    </div>
                    <input type="submit" class="action-button btn button login btn-block mb-4"
                        value="{{ translate('user_account_setting.change_password_button') }}" />
                </fieldset>
            </form>
        </div>
    </div>
</div>
@push('js')
    @error('new_password')
        <script>
            Swal.fire({
                icon: 'error',
                title: '{{ translate('user_account_setting.error') }}',
                text: {!! json_encode($message ?? '-') !!},
                confirmButtonText: 'OK',
                showConfirmButton: true
            });
        </script>
    @enderror
    <script>
        $("#toggle-pass").click(function() {
            var passwordInput = $("#old_passrword");

            if (passwordInput.attr("type") === "password") {
                passwordInput.attr("type", "text");
                $('#toggle-pass').addClass('fa-eye-slash')
                $('#toggle-pass').removeClass('fa-eye')
            } else {
                passwordInput.attr("type", "password");
                $('#toggle-pass').removeClass('fa-eye-slash')
                $('#toggle-pass').addClass('fa-eye')
            }
        });
        $("#toggle-pass1").click(function() {
            var passwordInput = $("#new_passrword");

            if (passwordInput.attr("type") === "password") {
                passwordInput.attr("type", "text");
                $('#toggle-pass1').addClass('fa-eye-slash')
                $('#toggle-pass1').removeClass('fa-eye')
            } else {
                passwordInput.attr("type", "password");
                $('#toggle-pass1').removeClass('fa-eye-slash')
                $('#toggle-pass1').addClass('fa-eye')
            }
        });
        $("#toggle-pass2").click(function() {
            var passwordInput = $("#confirm_new_passrword");

            if (passwordInput.attr("type") === "password") {
                passwordInput.attr("type", "text");
                $('#toggle-pass2').addClass('fa-eye-slash')
                $('#toggle-pass2').removeClass('fa-eye')
            } else {
                passwordInput.attr("type", "password");
                $('#toggle-pass2').removeClass('fa-eye-slash')
                $('#toggle-pass2').addClass('fa-eye')
            }
        });

        $("#password-alert-box").hide()
        $("#password_reset_form").on("submit", function(e) {

            e.preventDefault();
            let form_data = $(this).serialize();
            $.ajax({
                url: "{{ route('userprofile.pass_update') }}",
                type: "POST",
                data: form_data,
                success: function(response) {
                    if (response.status) {
                        $("#password-alert-box").hide()
                        Swal.fire({
                            title: "{{ translate('user_account_setting.success') }}",
                            text: "{{ translate('user_account_setting.password_updated') }}",
                            icon: "success"
                        }).then(() => {
                            window.location.reload();
                        });
                    } else {
                        $("#password-alert-box").show()
                        $("#password-alert-box p").html(response.message)
                    }
                }
            })
        })
    </script>
@endpush
