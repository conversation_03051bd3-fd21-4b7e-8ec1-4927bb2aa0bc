@extends('layouts.master')

@push('css')
    <link href="{{ asset('plugins/components/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" />
@endpush

@section('content')
    <section class="head_btn dash_head_sec">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_btns align-item-center">
                        <div class="title">
                            <h1>{{ $amenity->name }}</h1>
                        </div>
                        <div class="header_btns d-flex align-item-center gap-2">
                            <div class="nav_search main me-2">
                                <!-- Actual search box -->
                                <div class="form-group has-feedback has-search m-0">
                                    <form class="example" action="/action_page.php" style="width: 100%">
                                        <button type="button"><i class="fa fa-search"></i></button>
                                        <input type="text" placeholder="Search.." id="searchBar" class="searchBar"
                                            name="search">
                                        {{-- <i class="fa-solid fa-filter" style="color: #000000;"></i> --}}
                                    </form>
                                </div>
                            </div>
                            <a class="btn btn_trans topbar me-2" href="{{ url('cms') }}#amenities_pane">Back</a>
                            <a class="btn create_amenity_btn btn_yellow" data-amenity-id="{{ $amenity->id }}"
                                href="javascript:void(0)">
                                {{-- <i class="icon-plus"></i>  --}}
                                {{ __('add') }}
                                {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Amenity') }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <div class="container-fluid">
        <!-- .row -->
        <div class="row">
            <div class="col-sm-12">
                <div class="white-box">
                    {{-- <h3 class="box-title pull-left">
                        {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOptions') }}</h3> --}}
                    {{-- @can('add-' . str_slug('AmenityOptions')) --}}
                    {{-- <a class="btn  pull-right btn_yellow" href="{{ url('/amenityOption/amenity-option/create') }}"> --}}
                    {{-- <i class="icon-plus"></i>  --}}
                    {{-- {{ __('add') }}
                            {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOptions') }}</a> --}}
                    {{-- @endcan --}}
                    <div class="clearfix"></div>
                    <div class="table-responsive">
                        <table class="table" id="myTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    {{-- <th>{{ __('Amenity') }}</th> --}}
                                    <th>{{ __('name') }}</th>
                                    <th>{{ __('actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($amenityoptions as $item)
                                    <tr>
                                        <td>{{ $loop->iteration ?? $item->id }}</td>
                                        {{-- <td>{{ $item->amenity->name ?? '' }}</td> --}}
                                        <td>{{ $item->name }}</td>
                                        <td class="form_btn ">
                                            <div class="dropdown">
                                                <button class=" dropdown-toggle" type="button" id="dropdownMenuButton"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                </button>
                                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    {{-- @can('view-' . str_slug('AmenityOptions')) --}}
                                                    <a href="#!" data-toggle="modal" data-target="#amenityDetail"
                                                        title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOptions') }}">
                                                        <button class="dropdown-item view-detail"
                                                            data-amenity-id="{{ $item->id }}">
                                                            View
                                                        </button>
                                                    </a>
                                                    {{-- <a href="{{ url('/amenityOption/amenity-option/' . $item->id) }}"
                                                            title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOptions') }}">
                                                            <button class="dropdown-item">
                                                                View
                                                            </button>
                                                        </a> --}}

                                                    {{-- @endcan --}}
                                                    {{-- @can('edit-' . str_slug('AmenityOptions')) --}}
                                                    <a href="{{ url('/amenityOption/amenity-option/' . $item->id . '/edit') }}"
                                                        title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOptions') }}">
                                                        <button class="dropdown-item">
                                                            Edit
                                                        </button>
                                                    </a>
                                                    {{-- @endcan --}}
                                                    {{-- @can('delete-' . str_slug('AmenityOptions')) --}}
                                                    <form method="POST"
                                                        action="{{ url('/amenityOption/amenity-option' . '/' . $item->id) }}"
                                                        accept-charset="UTF-8" style="display:inline">
                                                        {{ method_field('DELETE') }}
                                                        {{ csrf_field() }}
                                                        <button type="submit" class="dropdown-item"
                                                            title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOptions') }}"
                                                            onclick="return confirm(&quot;Confirm delete?&quot;)">
                                                            Delete
                                                        </button>
                                                    </form>
                                                    {{-- @endcan --}}
                                                </div>
                                            </div>
                                        </td>

                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                        {{-- <div class="pagination-wrapper"> {!! $AmenityOptions->appends(['search' => Request::get('search')])->render() !!} </div> --}}
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Modal --}}
    <section class="report amenity popup">
        <div class="modal fade" id="amenityDetail" tabindex="-1" role="dialog" aria-labelledby="amenityDetail">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <span class="close" data-dismiss="modal">&times;</span>
                        <h1 class="modal-title" id="title">{{ __('amenity_detail') }}</h1>
                        <div class="form_field_padding">
                            <div class="mb-3 mod_cust_text">
                                <div class="amen_img">
                                    <img src="" alt="Amenity Image" class="img-fluid"
                                        onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;">
                                </div>
                                <p id="name"><span>{{ __('amenity_name') }} {{ trans('in_eng') }}:</span>
                                    <span class="info">WIFI</span>
                                </p>
                                <p id="amenityNameSpanish"><span>{{ __('amenity_name') }}
                                        {{ trans('in_spanish') }}:</span>
                                    <span class="info">WIFI</span>
                                </p>
                                <p id="desc_eng"><span>{{ __('description') }} {{ trans('in_eng') }}:</span>
                                    <span class="info">
                                        Lorem Ipsum
                                        is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been
                                        the
                                        industry's standard dummy text ever since the 1500s, when an unknown printer took a
                                        galley of type and scrambled it to make a type specimen book.
                                    </span>
                                </p>
                                <p id="desc_spanish"><span>{{ __('description') }} {{ trans('in_spanish') }}:</span>
                                    <span class="info">
                                        Lorem Ipsum
                                        is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been
                                        the
                                        industry's standard dummy text ever since the 1500s, when an unknown printer took a
                                        galley of type and scrambled it to make a type specimen book.
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('js')
    <script src="{{ asset('plugins/components/toast-master/js/jquery.toast.js') }}"></script>

    <script src="{{ asset('plugins/components/datatables/jquery.dataTables.min.js') }}"></script>
    <!-- start - This is for export functionality only -->
    <!-- end - This is for export functionality only -->
    <script>
        $(document).ready(function() {

            $(document).on('click', '.create_amenity_btn', function() {
                var amenityID = $(this).data('amenity-id');
                window.location.href = `{{ url('/amenityOption/amenity-option/create') }}?amenity-id=` +
                    amenityID;
            });
            @if (\Session::has('message'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session()->get('message') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 3000
                });
            @endif
        })

        $(function() {
            $('#myTable').DataTable({
                'aoColumnDefs': [{
                    'bSortable': false,
                    'aTargets': [-1] /* 1st one, start by the right */
                }],
                'language': {
                    'emptyTable': 'No amenities in this category'
                }
            });

        });
        $(document).on("click", ".view-detail", function() {
            var amenityID = $(this).data('amenity-id');

            $.ajax({
                url: "{{ url('amenityOption/amenity-option') }}" + '/' + amenityID,
                type: 'GET',
                success: function(response) {
                    if (response.status) {
                        var data = response.data;

                        // Set default values
                        var nameEn = "N/A";
                        var nameEs = "N/A";
                        var descEn = "N/A";
                        var descEs = "N/A";

                        // Extract translations
                        if (data.translations && data.translations.length > 0) {
                            data.translations.forEach(function(translation) {
                                if (translation.locale === 'en') {
                                    nameEn = translation.name;
                                    descEn = translation.description;
                                }
                                if (translation.locale === 'es') {
                                    nameEs = translation.name;
                                    descEs = translation.description;
                                }
                            });
                        }

                        // Populate modal fields
                        $('#amenityDetail').modal('show');
                        $('#name .info').html(nameEn);
                        $('#amenityNameSpanish .info').html(nameEs);
                        $('#desc_eng .info').html(descEn);
                        $('#desc_spanish .info').html(descEs);

                        // Set image with a fallback
                        var imagePath = data.image ? "{{ asset('website') }}/" + data.image :
                            "{{ asset('website/images/plcaeholderListingImg.png') }}";
                        $('.amen_img img').attr('src', imagePath);
                    } else {
                        alert("Error: Data not found.");
                    }
                },
                error: function(xhr) {
                    console.error("Error fetching amenity details:", xhr);
                }
            });
        });
    </script>
@endpush
