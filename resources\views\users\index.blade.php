@extends('layouts.master')

@push('css')
    <link href="{{ asset('plugins/components/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" />
@endpush

@section('content')
    <div class="container-fluid">
        <!-- .row -->
        <div class="row">
            <div class="col-sm-12">
                <div class="white-box">
                    <h3 class="box-title pull-left">Users List</h3>
                    <a class="btn btn-success pull-right" href="{{ url('user/create') }}"><i class="icon-plus"></i> Add
                        user</a>
                    <div class="clearfix"></div>
                    <hr>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table id="myTable" class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>Name</th>
                                            <th>Role</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($users as $key => $user)
                                            <tr>
                                                <td>{{ $key + 1 }}</td>
                                                <td>{{ $user->name }}</td>
                                                <td>{{ $user->roles()->pluck('name')->implode(', ') }}</td>
                                                <th>
                                                    <a href="{{ url('user/edit/' . $user->id) }}"><i class="icon-pencil"></i>
                                                        Edit</a> &nbsp;&nbsp;
                                                    <a class="delete" href="{{ url('user/delete/' . $user->id) }}"><i
                                                            class="icon-trash"></i> Delete</a>
                                                </th>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        @include('layouts.partials.right-sidebar')
    </div>
@endsection

@push('js')
    <script src="{{ asset('plugins/components/toast-master/js/jquery.toast.js') }}"></script>
    <script src="{{ asset('plugins/components/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="https://cdn.datatables.net/buttons/1.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function() {
            $(document).on('click', '.delete', function(e) {
                if (confirm('Are you sure want to delete?')) {} else {
                    return false;
                }
            });
            @if (\Session::has('message'))
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: {!! json_encode(session()->get('message')) !!},
                    showConfirmButton: false,
                    timer: 3000
                });
            @endif
        })

        $(function() {
            $('#myTable').DataTable({
                "columns": [
                    null, null, null, {
                        "orderable": false
                    }
                ]
            });

        });
    </script>
@endpush
