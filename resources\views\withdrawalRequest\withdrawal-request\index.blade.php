@extends('layouts.master')

@push('css')
    <link href="{{ asset('plugins/components/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" />
@endpush

@section('content')
    <div class="container-fluid">
        <!-- .row -->
        <div class="row">
            <div class="col-sm-12">
                <div class="white-box">
                    <h3 class="box-title pull-left">
                        {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'WithdrawalRequest') }}</h3>
                    @can('add-' . str_slug('WithdrawalRequest'))
                        <a class="btn btn_yellow pull-right" href="{{ url('/withdrawalRequest/withdrawal-request/create') }}"><i
                                class="icon-plus"></i> Add
                            {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'WithdrawalRequest') }}</a>
                    @endcan
                    <div class="clearfix"></div>
                    <hr>
                    <div class="table-responsive">
                        <table class="table" id="myTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>User</th>
                                    <th>Reason</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($withdrawalrequest as $item)
                                    <tr>
                                        <td>{{ $loop->iteration ?? $item->id }}</td>
                                        <td>{{ $item->user->name ?? '-' }}</td>
                                        <td>{{ $item->reason ?? 'No Comment' }}</td>
                                        <td>$ {{ $item->amount }}</td>
                                        <td>
                                            @if ($item->status == 0)
                                                Pending
                                            @else
                                                Reject
                                            @endif
                                        </td>
                                        <td class="form_btn ">
                                            <div class="dropdown">
                                                <button class=" dropdown-toggle" type="button" id="dropdownMenuButton"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                </button>
                                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    @can('view-' . str_slug('WithdrawalRequest'))
                                                        <a href="{{ url('/withdrawalRequest/withdrawal-request/' . $item->id) }}"
                                                            title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'WithdrawalRequest') }}">
                                                            <button class="dropdown-item">
                                                                {{-- <i class="fa fa-eye" aria-hidden="true"></i>  --}}
                                                                View
                                                            </button>
                                                        </a>
                                                    @endcan
                                                    @can('edit-' . str_slug('WithdrawalRequest'))
                                                        <a href="{{ url('/withdrawalRequest/withdrawal-request/' . $item->id . '/edit') }}"
                                                            title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'WithdrawalRequest') }}">
                                                            <button class="dropdown-item">
                                                                {{-- <i class="fa fa-pencil-square-o" aria-hidden="true"></i>  --}}
                                                                Edit
                                                            </button>
                                                        </a>
                                                    @endcan
                                                    @can('delete-' . str_slug('WithdrawalRequest'))
                                                        <form method="POST"
                                                            action="{{ url('/withdrawalRequest/withdrawal-request' . '/' . $item->id) }}"
                                                            accept-charset="UTF-8" style="display:inline">
                                                            {{ method_field('DELETE') }}
                                                            {{ csrf_field() }}
                                                            <button type="submit" class="dropdown-item"
                                                                title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'WithdrawalRequest') }}"
                                                                onclick="return confirm(&quot;Confirm delete?&quot;)">
                                                                {{-- <i class="fa fa-trash-o" aria-hidden="true"></i>  --}}
                                                                Delete
                                                            </button>
                                                        </form>
                                                    @endcan
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">You don’t have any withdrawal request</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                        <div class="pagination-wrapper"> {!! $withdrawalrequest->appends(['search' => Request::get('search')])->render() !!} </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="{{ asset('plugins/components/toast-master/js/jquery.toast.js') }}"></script>

    <script src="{{ asset('plugins/components/datatables/jquery.dataTables.min.js') }}"></script>
    <!-- start - This is for export functionality only -->
    <!-- end - This is for export functionality only -->
    <script>
        $(document).ready(function() {

            @if (\Session::has('message'))
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: '{{ session('message') }}',
                    showConfirmButton: false
                });
            @endif
        })

        $(function() {
            $('#myTable').DataTable({
                'aoColumnDefs': [{
                    'bSortable': false,
                    'aTargets': [-1] /* 1st one, start by the right */
                }]
            });

        });
    </script>
@endpush
