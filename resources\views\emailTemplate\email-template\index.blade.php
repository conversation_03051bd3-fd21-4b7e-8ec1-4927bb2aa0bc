@extends('layouts.master')

@push('css')
    <link href="{{ asset('plugins/components/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" />
@endpush

@section('content')
    <div class="container-fluid">
        <!-- .row -->
        <div class="row">
            <div class="col-sm-12">
                <div class="white-box">
                    <h3 class="box-title pull-left">
                        {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'EmailTemplate') }}</h3>
                    @can('add-' . str_slug('EmailTemplate'))
                        <a class="btn btn_yellow pull-right" href="{{ url('/emailTemplate/email-template/create') }}">
                            {{-- <i class="icon-plus"></i>  --}}
                            Add
                            {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'EmailTemplate') }}</a>
                    @endcan
                    <div class="clearfix"></div>
                    <hr>
                    <div class="table-responsive">
                        <table class="table" id="myTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Purpose</th>
                                    <th>Subject</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($emailtemplate as $item)
                                    <tr>
                                        <td>{{ $loop->iteration ?? $item->id }}</td>
                                        <td>{{ $item->purpose }}</td>
                                        <td>{{ $item->subject }}</td>
                                        <td class="form_btn ">
                                            <div class="dropdown">
                                                <button class=" dropdown-toggle" type="button" id="dropdownMenuButton"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                </button>
                                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    @can('view-' . str_slug('EmailTemplate'))
                                                        <a href="" data-toggle="modal" data-target="#emailTemp"
                                                            title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'EmailTemplate') }}">
                                                            <button class="dropdown-item">
                                                                View
                                                            </button>
                                                        </a>
                                                        {{-- <a href="{{ url('/emailTemplate/email-template/' . $item->id) }}"
                                                            title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'EmailTemplate') }}">
                                                            <button class="dropdown-item">
                                                                View
                                                            </button>
                                                        </a> --}}
                                                    @endcan
                                                    @can('edit-' . str_slug('EmailTemplate'))
                                                        <a href="{{ url('/emailTemplate/email-template/' . $item->id . '/edit') }}"
                                                            title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'EmailTemplate') }}">
                                                            <button class="dropdown-item">
                                                                Edit
                                                            </button>
                                                        </a>
                                                    @endcan
                                                    @can('delete-' . str_slug('EmailTemplate'))
                                                        @if (!in_array($item->id, [1]))
                                                            <form method="POST"
                                                                action="{{ url('/emailTemplate/email-template' . '/' . $item->id) }}"
                                                                accept-charset="UTF-8" style="display:inline">
                                                                {{ method_field('DELETE') }}
                                                                {{ csrf_field() }}
                                                                <button type="submit" class="dropdown-item"
                                                                    title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'EmailTemplate') }}"
                                                                    onclick="return confirm(&quot;Confirm delete?&quot;)">
                                                                    Delete
                                                                </button>
                                                            </form>
                                                        @endif
                                                    @endcan
                                                </div>
                                            </div>
                                        </td>


                                        {{-- <td>
                                            @can('view-' . str_slug('EmailTemplate'))
                                                <a href="{{ url('/emailTemplate/email-template/' . $item->id) }}"
                                                    title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'EmailTemplate') }}">
                                                    <button class="btn btn-info btn-sm">
                                                        <i class="fa fa-eye" aria-hidden="true"></i> View
                                                    </button>
                                                </a>
                                            @endcan
                                            @can('edit-' . str_slug('EmailTemplate'))
                                                <a href="{{ url('/emailTemplate/email-template/' . $item->id . '/edit') }}"
                                                    title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'EmailTemplate') }}">
                                                    <button class="btn btn-primary btn-sm">
                                                        <i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit
                                                    </button>
                                                </a>
                                            @endcan
                                            @can('delete-' . str_slug('EmailTemplate'))
                                                @if (!in_array($item->id, [1]))
                                                    <form method="POST"
                                                        action="{{ url('/emailTemplate/email-template' . '/' . $item->id) }}"
                                                        accept-charset="UTF-8" style="display:inline">
                                                        {{ method_field('DELETE') }}
                                                        {{ csrf_field() }}
                                                        <button type="submit" class="btn btn-danger btn-sm"
                                                            title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'EmailTemplate') }}"
                                                            onclick="return confirm(&quot;Confirm delete?&quot;)"><i
                                                                class="fa fa-trash-o" aria-hidden="true"></i> Delete
                                                        </button>
                                                    </form>
                                                @endif
                                            @endcan
                                        </td> --}}
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                        <div class="pagination-wrapper"> {!! $emailtemplate->appends(['search' => Request::get('search')])->render() !!} </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Modal --}}
    <section class="report emailTemp popup">
        <div class="modal fade" id="emailTemp" tabindex="-1" role="dialog" aria-labelledby="emailTemp">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <span class="close" data-dismiss="modal">&times;</span>
                        <h1 class="modal-title" id="title">{{ __('email_temp') }}</h1>
                        <div class="form_field_padding">
                            <div class="mb-3 mod_cust_text">
                                <p id="title"><span>{{ __('subject') }}:</span>
                                    <span class="info">
                                        Email Verification
                                    </span>
                                </p>
                                <p id="short_description"><span>{{ __('description') }}:</span>
                                    <span class="info">
                                        This is testing helpcenter description
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </section>
@endsection

@push('js')
    <script src="{{ asset('plugins/components/toast-master/js/jquery.toast.js') }}"></script>

    <script src="{{ asset('plugins/components/datatables/jquery.dataTables.min.js') }}"></script>
    <!-- start - This is for export functionality only -->
    <!-- end - This is for export functionality only -->
    <script>
        $(document).ready(function() {

            @if (\Session::has('message'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('message') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 3000
                });
            @endif
        })

        $(function() {
            $('#myTable').DataTable({
                'aoColumnDefs': [{
                    'bSortable': false,
                    'aTargets': [-1] /* 1st one, start by the right */
                }]
            });

        });
    </script>
@endpush
