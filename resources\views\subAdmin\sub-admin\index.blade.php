@extends('layouts.master')

@push('css')
    <link href="{{ asset('plugins/components/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" />
@endpush

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <h3 class="box-title pull-left">
                    {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'SubAdmin') }}</h3>
                @can('add-' . str_slug('SubAdmin'))
                    <a class="btn btn_yellow pull-right" href="{{ url('/subAdmin/sub-admin/create') }}"><i
                            class="icon-plus"></i> Add
                        {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'SubAdmin') }}</a>
                @endcan
            </div>
        </div>
        <!-- .row -->
        <div class="row">
            <div class="col-sm-12">
                <div class="white-box">
                    <div class="table-responsive">
                        <table class="table" id="myTable">
                            <thead>
                                <tr>
                                    {{-- <th>#</th> --}}
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($subadmin as $item)
                                    @if ($item->hasRole('sub_admin') || $item->hasRole('user'))
                                        <tr>
                                            {{-- <td>{{ $loop->iteration ?? $item->id }}</td> --}}
                                            <td>{{ $item->name }}</td>
                                            <td>{{ $item->email }}</td>
                                            <td>
                                                @if ($item->hasRole('user'))
                                                    Super Admin
                                                @else
                                                    Sub Admin
                                                @endif
                                            </td>
                                            <td class="form_btn ">
                                                <div class="dropdown">
                                                    <button class=" dropdown-toggle" type="button" id="dropdownMenuButton"
                                                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                        <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                    </button>
                                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                        @can('view-' . str_slug('SubAdmin'))
                                                            <a href="{{ url('/subAdmin/sub-admin/' . $item->id) }}"
                                                                title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'SubAdmin') }}">
                                                                <button class="dropdown-item ">View
                                                                </button>
                                                            </a>
                                                        @endcan
                                                        @can('edit-' . str_slug('SubAdmin'))
                                                            <a href="{{ url('/subAdmin/sub-admin/' . $item->id . '/edit') }}"
                                                                title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'SubAdmin') }}">
                                                                <button class="dropdown-item"> Edit
                                                                </button>
                                                            </a>
                                                        @endcan
                                                        @can('delete-' . str_slug('SubAdmin'))
                                                            <form method="POST"
                                                                action="{{ url('/subAdmin/sub-admin' . '/' . $item->id) }}"
                                                                accept-charset="UTF-8" style="display:inline">
                                                                {{ method_field('DELETE') }}
                                                                {{ csrf_field() }}
                                                                <button type="submit" class="dropdown-item"
                                                                    title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'SubAdmin') }}"
                                                                    onclick="return confirm(&quot;Confirm delete?&quot;)">
                                                                    Delete
                                                                </button>
                                                            </form>
                                                        @endcan
                                                    </div>
                                                </div>


                                            </td>
                                            <td>
                                                @can('view-' . str_slug('SubAdmin'))
                                                    <a href="{{ url('/subAdmin/sub-admin/' . $item->id) }}"
                                                        title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'SubAdmin') }}">
                                                        <button class="btn btn-info btn-sm">
                                                            <i class="fa fa-eye" aria-hidden="true"></i> View
                                                        </button>
                                                    </a>
                                                @endcan
                                                @can('edit-' . str_slug('SubAdmin'))
                                                    <a href="{{ url('/subAdmin/sub-admin/' . $item->id . '/edit') }}"
                                                        title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'SubAdmin') }}">
                                                        <button class="dropdown-item"> Edit
                                                        </button>
                                                    </a>
                                                @endcan
                                                @can('delete-' . str_slug('SubAdmin'))
                                                    <form method="POST"
                                                        action="{{ url('/subAdmin/sub-admin' . '/' . $item->id) }}"
                                                        accept-charset="UTF-8" style="display:inline">
                                                        {{ method_field('DELETE') }}
                                                        {{ csrf_field() }}
                                                        <button type="submit" class="dropdown-item"
                                                            title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'SubAdmin') }}"
                                                            onclick="return confirm(&quot;Confirm delete?&quot;)"> Delete
                                                        </button>
                                                    </form>
                                                @endcan
                                            </td>
                                        </tr>
                                    @endif
                                @endforeach
                            </tbody>
                        </table>
                        <div class="pagination-wrapper"> {!! $subadmin->appends(['search' => Request::get('search')])->render() !!} </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="{{ asset('plugins/components/toast-master/js/jquery.toast.js') }}"></script>

    <script src="{{ asset('plugins/components/datatables/jquery.dataTables.min.js') }}"></script>
    <!-- start - This is for export functionality only -->
    <!-- end - This is for export functionality only -->
    <script>
        $(document).ready(function() {

            @if (\Session::has('message'))
                Swal.fire({
                    position: 'top',
                    icon: 'success',
                    title: 'Success!',
                    text: @json(session('message')),
                    showConfirmButton: false,
                    timer: 3000
                });
            @endif
        })

        $(function() {
            $('#myTable').DataTable({
                'aoColumnDefs': [{
                    'bSortable': false,
                    'aTargets': [-1] /* 1st one, start by the right */
                }]
            });

        });
    </script>
@endpush
