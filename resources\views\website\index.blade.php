@extends('website.layout.master')
@push('css')
    {{-- <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/pikaday/css/pikaday.css"> --}}
    <link href="{{ asset('plugins/components/icheck/skins/all.css') }}" rel="stylesheet">
    <link href="{{ asset('website/assets_cdn/flatpicker/flatpicker.css') }}" rel="stylesheet" />
    <style>
        .empty_listing {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 30vh;
            font-size: 30px;
            font-weight: bold;
        }

        .loader {
            border: 16px solid #f3f3f3;
            border-radius: 50%;
            border-top: 16px solid #171717;
            ;
            width: 120px;
            height: 120px;
            -webkit-animation: spin 1s linear infinite;
            /* Safari */
            animation: spin 1s linear infinite;
            display: block;
            margin: 150px auto;
        }

        #listing_map {
            height: 100%;
            width: 100%;
            margin: 0px;
            padding: 0px;
        }

        #listing_map .gm-svpc {
            border-radius: 10px !important;
        }

        #listing_map .gm-style-cc {
            display: none;
        }

        #listing_map a:has(img[alt="Google"]) {
            display: none !important;
        }

        .gm-bundled-control .gmnoprint:has([class*="gm-control"])>div {
            border-radius: 10px !important;
        }

        #listing_map .gm-style-mtc-bbw .gm-style-mtc:first-of-type>button {
            border-radius: 10px 0 0 10px;
        }

        #listing_map .gm-style-mtc-bbw .gm-style-mtc:last-of-type>button {
            border-radius: 0 10px 10px 0;
        }

        #listing_map button.gm-control-active.gm-fullscreen-control,
        .listing_map #map_location .gmnoprint button[aria-label="Zoom in"],
        .listing_map #map_location .gmnoprint button[aria-label="Zoom out"],
        .listing_map #map_location .gmnoprint button.gm-control-active {
            border-radius: 10px !important;
        }

        #listing_map {
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        body .pac-container {
            padding: 10px 15px;
            width: 600px !important;
            border-radius: 30px;
        }

        body .pac-container .pac-item {
            min-height: 70px;
            position: relative;
            padding-left: 70px;
            display: flex;
            justify-content: center;
            flex-direction: column;
        }

        body .pac-container .pac-item .pac-icon {
            height: 50px;
            width: 50px;
            background-color: rgba(0, 0, 0, 1);
            border-radius: 10px;
            background-size: 20px;
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            margin: auto 0;
        }

        #card-view .more_listing_spinner {
            width: 150px;
            height: 50px;
            background: transparent;
            border-radius: 50px;
            color: black;
            margin: 0 auto;
            font-family: 'Poppins';
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 5px;
        }

        body .pac-container .pac-item .pac-item-query+span {
            white-space: pre-wrap;
            line-height: 16px;
            font-size: 13px;
        }

        body .pac-container .pac-item .pac-item-query {
            line-height: 16px
        }

        body .pac-container .pac-item {
            border-radius: 20px
        }

        /* Location dropdown center align style */

        /* .pac-container {
                                                                                                                                                                                                                                    left: 28% !important;
                                                                                                                                                                                                                                    transform: translateX(-50%) !important;
                                                                                                                                                                                                                                    width: auto !important;
                                                                                                                                                                                                                                    min-width: 250px;
                                                                                                                                                                                                                                    z-index: 1050;
                                                                                                                                                                                                                                } */

        /* Location dropdown center align style */

        /* Safari */
        @-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* date css */
        input[name=daterange] {
            width: 100%;
        }

        .daterangepicker td.active,
        .daterangepicker td.active:hover,
        .daterangepicker .drp-buttons .btn.btn-primary {
            border: 0;
            background-color: var(--btn-yellow);
        }

        .daterangepicker td.active {
            border-radius: 50%;
        }

        /* .daterangepicker .start-date + .in-range { border-radius: 15px 0 0 15px; }
                                                                                                                                                                                                                                                                                                                                                                                            .daterangepicker tr:has(.end-date) .in-range:nth-last-of-type(3):not(.today) { border-radius: 0 15px 15px 0; } */

        .backdrop {
            backdrop-filter: brightness(1.5);
        }

        .map_product {
            padding-top: 0px;
        }

        .product {
            margin-bottom: 0px;
        }

        .map_product .product_box p:not(.star-rate) {
            height: unset;
        }

        .map_product .product_box:hover {
            transform: unset;
        }



        .flatpickr-calendar:not(.noCalendar) {
            /* max-width: 100% !important; Prevents overflow on smaller screens */
            padding-top: 5px;
            /* Fixed padding */
        }

        .flatpickr-calendar .flatpickr-innerContainer {
            height: 100% !important;
            /* Full height of parent */
            width: 100% !important;
            /* Full width of parent */
            padding: 0 !important;
            /* Remove any default padding that might interfere */
        }



        .flatpickr-calendar .flatpickr-months {
            padding-bottom: 20px !important;
            /* Adds space at the bottom */
        }

        .flatpickr-calendar .flatpickr-days {
            height: 100% !important;
            /* Let JS control the overall height */
            display: grid !important;
            /* Ensure grid layout for days */
            grid-template-columns: repeat(7, 1fr) !important;
            /* 7 columns for days of the week */
        }

        .flatpickr-calendar .flatpickr-day {
            height: 40px !important;
            /* Reduced from 50px to fit better */
            line-height: 40px !important;
            /* Matches height for vertical centering */
            font-size: 16px !important;
            /* Reduced from 18px for better fit */
        }

        .flatpickr-months .flatpickr-prev-month,
        .flatpickr-months .flatpickr-next-month {
            top: 5px;
        }

        .flatpickr-calendar:not(.hasTime) {
            left: 0 !important;
            right: 0 !important;
            margin: 0 auto;
        }

        #listing_map .map_product .product_box .description-wrapper {
            position: relative;
            max-width: 100%;
        }

        #listing_map .map_product .product_box .description-content {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            max-height: 45px;
            line-height: 1.6em;
        }

        #listing_map .map_product .product_box .description-content p {
            font-size: 14px;
        }

        #listing_map .map_product .product_box .show-more {
            display: block;
            margin-top: 3px;
            color: blue;
            cursor: pointer;
            /* text-decoration: underline; */
            font-size: 14px;
            text-transform: lowercase;
        }

        #listing_map .map_product .product_box .rating {align-items: center;}
        /* #listing_map .map_product .product_box .rating h5, #listing_map .map_product .product_box .rating p {margin-bottom: 0;} */
        /* #listing_map .gm-style .gm-style-iw-d {padding-right: 12px;} */


    </style>
@endpush
@section('content')
    <div class="backdrop h-100 w-100"></div>
    <section class="topbar">
        <div class="container">
            <div class="row top-rw">
                <div class="col-md-12">
                    <form id="search-box">
                        <div class="parent row justify-content-between">
                            <div class="top_box1 top_categories_wrapper col-xxl-12 justify-content-center align-items-center">
                                @foreach ($categories as $key => $category)
                                    @if ($category->id != 5)
                                        <div class="box">
                                            <input type="radio" class="radio_btn" id="box-{{ $category->id }}"
                                                name="category_id" value="{{ $category->id }}"
                                                {{ $key == 0 ? 'checked' : '' }}>
                                            <label for="box-{{ $category->id }}">
                                                <div class="find_btn d-flex flex-column align-items-center">
                                                    <div class="radio_img">
                                                        <img src="{{ asset('website') . '/' . $category->image }}"
                                                            alt="">
                                                    </div>
                                                    {{ $category->display_name }}
                                                </div>
                                            </label>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                            <div class="col-xxl-9 col-xl-8 col-12 filter_wrapper p-0">
                                <div class="d-flex justify-content-between align-items-center p-0">
                                    <div class="col-lg-4 col-md-6 col-12 single_input_wrapper">
                                        <label for="autocomplete" class="input_wrapper w-100">
                                            <label for="autocomplete" class="location">{{ translate('home.where') }}</label>
                                            <div class="d-btn ">
                                                <input type="text" id="autocomplete" name="address"
                                                    class="search_input form-control notranslate"
                                                    placeholder="Enter destination" autocomplete="none"
                                                    value="{{ $listing->address ?? '' }}">
                                            </div>
                                            <i class="fas fa-times position-absolute remove_btn"></i>
                                        </label>
                                        <div id="map_canvas"></div>
                                        <input id="lat" class="search_input" name="lat" type="text"
                                            value="" hidden/>
                                        <input id="lng" class="search_input" name="lng" type="text"
                                            value="" hidden/>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-12  date_wrapper single_input_wrapper">
                                        <label for="datePicker" class="input_wrapper date_input_wrapper w-100">
                                            <label for="datePicker" class="datePicker_label">{{ translate('home.select_date') }}</label>
                                            <div class="d-btn d-flex">
                                                {{-- <div class="input_wrapper_acc"> --}}
                                                    {{-- <label for="dateExp" class="datePicker_label"></label> --}}
                                                    <input type="text" id="dateExp" autocomplete="off" name="dateExp"
                                                        value="" placeholder="{{ translate('home.checkin_date') }}"
                                                        class="singlePicker w-50" />
                                                {{-- </div> --}}
                                                {{-- <div class="input_wrapper_acc"> --}}
                                                    {{-- <label for="dateExpEnd" class="datePicker_label">{{ translate('home.checkout_date') }}</label> --}}
                                                    <input type="text" id="dateExpEnd" autocomplete="off" name="dateExpEnd"
                                                        value="" placeholder="{{ translate('home.checkout_date') }}"
                                                        class="singlePicker w-50" />
                                                {{-- </div> --}}
                                                <input type="hidden" name="date_ranges" class="date_ranges">
                                                <input type="text" id="datePicker" value autocomplete="off"
                                                    name="daterange" value=""
                                                    placeholder="{{ translate('home.checkin_checkout') }}"
                                                    class= "rangePicker" />

                                                {{-- <label for="datePicker"><i class="fas fa-calendar-alt"></i></label> --}}
                                            </div>
                                            <i class="fas fa-times position-absolute remove_btn"></i>
                                        </label>
                                    </div>
                                    <input type="hidden" id="listing_name_search" name="listing_name"
                                        value="{{ request()->get('listing_name') }}">
                                    <div
                                        class="col-lg-4 col-12 input_wrapper button_wrapper d-flex justify-content-between align-items-center gap-1 single_input_wrapper">
                                        <div class="input_guests w-100 cursor-pointer">
                                            <div class="dropdown">
                                                <button class="guest_btn d-flex flex-column w-100" type="button"
                                                    id="guests" data-bs-toggle="dropdown" aria-expanded="false"
                                                    data-bs-auto-close="false">
                                                    <label for="guests"
                                                        class="guests">{{ translate('home.participants') }}</label>
                                                    <p class="m-0"><span
                                                            class="number_guests fs-14">{{ translate('home.how_many') }}</span>
                                                    </p>
                                                </button>
                                                @include('website.template.guests_dropdown')
                                            </div>
                                            {{-- <div class="d-btn">
                                                <input type="number" id="guests" value autocomplete="off" name="guests"
                                                    value="" placeholder="" />
                                            </div> --}}
                                        </div>
                                        <div class="d-flex align-items-center gap-1">
                                            <div class="submit">
                                                <div class="s-btn-icon">
                                                    <input type="submit" id="search_form" value=""
                                                        class="search d-none">
                                                    <label for="search_form" class="trans_btn">
                                                        <i class="fas fa-search"></i>
                                                    </label>
                                                </div>
                                            </div>
                                            @include('website.template.advance_search')
                                            {{-- <button type="button" class="reset_btn trans_btn"><i
                                                    class="bi bi-arrow-counterclockwise"></i>
                                            </button> --}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
    {{-- List View button --}}
    {{-- <div class="text-center">
         <button class="map-btn button mb-4" id="view_map" style="display: none">
            Show Map
            <img src="{{ asset('website/images/map-home.png') }}" alt="">
        </button>
    </div> --}}
    <section class="parent_view justify-content-between gap-5 pt-5 px-0">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    {{-- Product section --}}
                    <section class="product" id="card-view">
                        <div class="container">
                            {{-- loader --}}
                            <div id="listing-loading">
                                <div class="loader"></div>
                            </div>
                            {{-- loader end --}}
                            <div class="row" id="listing_row">

                            </div>
                            {{-- <button class="Load_more btn btn-primary" type="button">{{ translate('home.load_more') }}</button> --}}
                            <div class="more_listing_spinner">

                            </div>
                            {{-- <div class="row">
                                <div class="col-lg-12">
                                    <div class="Load_more">
                                        <button class="button">Load More</button>
                                    </div>
                                </div>
                            </div> --}}
                        </div>
                    </section>
                </div>
                <div class="col-md-4">
                    <section style="text-align: center" class="show_map_sectiom hidden h-100">
                        <div id="listing_map" style="display: block;"
                            data-custom-marker-url="{{ asset('website/images/Avatar.png') }}">
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </section>
    <!-- Residence Modal -->
    @include('website.template.adv_acc')

    {{-- Car Modal --}}
    @include('website.template.adv_vehicle')

    {{-- Boat Modal  --}}
    @include('website.template.adv_boat')

    {{-- Tour Modal --}}
    @include('website.template.adv_exp')
@endsection
@push('js')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.0.1/angular.min.js"></script>
    <script src="https://momentjs.com/downloads/moment.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/js/select2.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script type="text/javascript"
        src="https://cdnjs.cloudflare.com/ajax/libs/ion-rangeslider/2.3.0/js/ion.rangeSlider.min.js"></script>
    {{-- <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDF1KUigTPIJOGIoRVEV9u7M9Tyx_Wzxj0&libraries=places" type="text/javascript"></script> --}}
    {{-- <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDF1KUigTPIJOGIoRVEV9u7M9Tyx_Wzxj0&callback=initMap" async defer></script> --}}
    <script>
        $('.topbar .top_box1 .box [name="category_id"]').change(function() {
            var selectedValue = $(this).closest('.box').find('input:checked').val();
            if (selectedValue == 1) {
                $('.topbar .filter_btn_wrapper #adv_filter').attr('data-bs-target', '#tour_filter_category')
            } else if (selectedValue == 2) {
                $('.topbar .filter_btn_wrapper #adv_filter').attr('data-bs-target', '#boat_filter_category')
            } else if (selectedValue == 3) {
                $('.topbar .filter_btn_wrapper #adv_filter').attr('data-bs-target', '#car_filter_category')
            } else if (selectedValue == 4) {
                $('.topbar .filter_btn_wrapper #adv_filter').attr('data-bs-target', '#filter_category')
            } else {
                $('.topbar .filter_btn_wrapper #adv_filter').attr('data-bs-target', '')
            }
        });
        $("button#adv_filter").on("click", function() {
            let datepicker = $("#datePicker").val();
            let lng = $("#lng").val();
            let lat = $("#lat").val();
            $(".date_range_placeholder").val(datepicker);
            $(".lng_placeholder").val(lng);
            $(".lat_placeholder").val(lat);
        });

        $('#dateExp').on('change', function(e) {
            var selectedValue = $(this).val();
            console.log('Rameel Selected Value: ' + selectedValue);
            // if(selectedValue != ''){
                $('.date_input_wrapper .datePicker_label').html(
                '<span class="w-50 fs-14 d-block">Check-in </span><span class="w-50 fs-14 d-block">Check-out </span>')
                $('.date_input_wrapper .datePicker_label').css('display','inline-flex');
                $('.date_input_wrapper .datePicker_label').css('width','100%');
                $('.date_input_wrapper .datePicker_label span').css('flex','1');
                // }else{
                // $('.date_input_wrapper .datePicker_label').html('');
            // }
        });

        $(document).on('change', '.filter_wrapper .input_wrapper input', function() {
            var address = $(this).val();
            var parent_address = $(this).closest('input_wrapper');
            if (address != '') {
                $(this).parent().next().addClass('show');
            } else {
                $(this).parent().next().removeClass('show');
            }
        });
    </script>
    <script>
        function loadUrlParameters() {
            const urlParams = new URLSearchParams(window.location.search);
            const categoryId = urlParams.get('category_id') || '';
            const lat = urlParams.get('lat') || @json(session('lat'));
            const lng = urlParams.get('lng') || @json(session('lng'));
            const adults = urlParams.get('adults') || '';
            const children = urlParams.get('children') || '';
            const passenger = urlParams.get('passenger') || '';
            const guest = urlParams.get('guest') || '';

            // Check if we're on a location search page
            const currentPath = window.location.pathname;
            const isLocationSearch = currentPath.startsWith('/search/');

            if (isLocationSearch) {
                // Extract location slug from URL path
                const locationSlug = currentPath.replace('/search/', '');

                // Try to get original address from sessionStorage first
                let locationName = sessionStorage.getItem('original_address_' + locationSlug);

                // If not found, convert slug back to readable location name
                if (!locationName) {
                    locationName = locationSlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                }

                // Set the address field
                $('#autocomplete').val(locationName);
            }

            // Set inputs
            $(`input[name="category_id"][value="${categoryId}"]`).prop('checked', true);
            $('#lat').val(lat);
            $('#lng').val(lng);
            if (adults !== null && adults !== '') {
                $('#adults').val(adults);
            }
            if (children !== null && children !== '') {
                $('#children').val(children);
            }
            if(categoryId !== null && categoryId !== ''){
                $("#listing_map").show();
                 $("#view_map").show();
                $('#card-view').parent().addClass('col-md-8').removeClass('col-md-12');
                $('.topbar .filter_btn_wrapper ').removeClass('hidden');
                    setInterval(() => {
                        $('.show_map_sectiom').removeClass('hidden');
                    }, 2000);
                    $('.guest_btn, .members_dropdown').removeClass('show');
                    $('.guest_btn ').attr('aria-expanded', false);
                }
                // alert("Er")
            return {
                category_id: categoryId,
                lat: lat,
                lng: lng,
                adults: parseInt(adults) || 0,
                passenger: parseInt(passenger) || 0,
                children: parseInt(children) || 0,
                guest: parseInt(guest) || 0
            };
        }

        function setUrlParams(params, pushToHistory = true) {
            const url = new URL(window.location.href);
            // First, clear all existing params
            url.search = '';
            // Add only the provided (non-empty) parameters
            for (const key in params) {
                const value = params[key];
                if (value !== null && value !== undefined && value !== '') {
                    url.searchParams.set(key, value);
                }
            }
            if (pushToHistory) {
                window.history.pushState({}, '', url);
            } else {
                window.history.replaceState({}, '', url);
            }
        }

        function updateTotalGuests(values = {}) {
            let total_guests = 0;
            let guestText = '';

            // Use values from parameter if available, else from DOM
            const category_selected = values.category_id || $('.parent input[name="category_id"]:checked').val();

            if (category_selected == '2') {
                const passenger = parseInt(values.passenger ?? $('#passenger').val()) || 0;
                total_guests = passenger;
                guestText = passenger === 1 ? '1 passenger' : `${passenger} passengers`;

            } else if (category_selected == '3') {
                const seat = parseInt(values.seat ?? $('#seat').val()) || 0;
                total_guests = seat;
                guestText = seat === 1 ? '1 seat' : `${seat} seats`;

            } else if (category_selected == '4') {
                const guest = parseInt(values.guest ?? $('#guest').val()) || 0;
                const acc_child = parseInt(values.acc_children ?? $('#acc_children').val()) || 0;
                total_guests = guest + acc_child;

                const guestLabel = guest === 1 ? '1 adult' : guest > 1 ? `${guest} adults` : '';
                const childLabel = acc_child === 1 ? '1 child' : acc_child > 1 ? `${acc_child} children` : '';

                if (guestLabel && childLabel) {
                    guestText = `${guestLabel} and ${childLabel}`;
                } else {
                    guestText = guestLabel || childLabel;
                }

            } else {
                const adult = parseInt(values.adults ?? $('#adults').val()) || 0;
                const child = parseInt(values.children ?? $('#children').val()) || 0;
                total_guests = adult + child;

                const adultLabel = adult === 1 ? '1 adult' : adult > 1 ? `${adult} adults` : '';
                const childLabel = child === 1 ? '1 child' : child > 1 ? `${child} children` : '';

                if (adultLabel && childLabel) {
                    guestText = `${adultLabel} and ${childLabel}`;
                } else {
                    guestText = adultLabel || childLabel;
                }
            }

            if (total_guests > 0) {
                $('.input_wrapper .guest_btn .number_guests').text(guestText);
                $('.input_wrapper .guest_btn span').addClass('guest_changed');
            }else{
                $('.input_wrapper .guest_btn .number_guests').text(@json(translate('home.how_many')));
                $('.input_wrapper .guest_btn span').removeClass('guest_changed');
            }
        }


        function showRangeDatePicker(allowSameDate = true) {
            $('#dateExp').val('').attr('disabled', true).hide();
            $('#dateExpEnd').val('').attr('disabled', true).hide();
            $('#datePicker').val('').attr('disabled', false).show();

            function formatDate(date) {
                const day = date.getDay();
                const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thurs", "Fri", "Sat"];
                const dayDate = date.getDate();
                const month = date.toLocaleDateString('en-US', {
                    month: 'short'
                });
                return `${dayNames[day]}, ${month} ${dayDate}`;
            }

            const urlParams = new URLSearchParams(window.location.search);
            const dateRange = urlParams.get('date_ranges');
            let preSelectedDates = [];

            if (dateRange) {
                const dates = dateRange.split(' : ');
                if (dates.length === 2) {
                    const start = new Date(dates[0]);
                    const end = new Date(dates[1]);
                    if (!isNaN(start) && !isNaN(end)) {
                        preSelectedDates = [start, end];
                        $('.date_ranges').val(`${dates[0]} : ${dates[1]}`);
                        $('#datePicker').val(`${formatDate(start)} — ${formatDate(end)}`);
                    }
                }
            }

            flatpickr('#datePicker', {
                mode: 'range',
                dateFormat: 'd-M-Y',
                showMonths: 2,
                minDate: 'today',
                position: "auto center",
                defaultDate: preSelectedDates,
                onValueUpdate: function(selectedDates, dateStr, instance) {
                    const currentMonth = instance.currentMonth;
                    const currentYear = instance.currentYear;

                    setTimeout(() => {
                        instance.changeMonth(currentMonth - instance.currentMonth);
                        instance.changeYear(currentYear);
                    }, 0);
                },
                onChange: function(selectedDates, dateStr, instance) {
                    if (!selectedDates.length) return;

                    const formattedDates = selectedDates.map(date => {
                        const year = date.getFullYear();
                        const month = String(date.getMonth() + 1).padStart(2, '0');
                        const day = String(date.getDate()).padStart(2, '0');
                        return `${year}-${month}-${day}`;
                    });

                    $('.date_ranges').val(formattedDates.join(' : '));
                    //set url


                    const formatDate = (date) => {
                        const day = date.getDay();
                        const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thurs", "Fri", "Sat"];
                        const dayDate = date.getDate();
                        const month = date.toLocaleDateString('en-US', {
                            month: 'short'
                        });
                        return `${dayNames[day]}, ${month} ${dayDate}`;
                    };

                    if (selectedDates.length === 1) {
                        instance.input.value = formatDate(selectedDates[0]);
                        instance._selectedSingleDate = selectedDates[0];
                    } else if (selectedDates.length === 2) {
                        const start = selectedDates[0];
                        const end = selectedDates[1];

                        if (!allowSameDate && start.getTime() === end.getTime()) {
                            instance.clear();
                            Swal.fire({
                                text: @json(translate('home.please_select_different_dates')),
                                icon: 'error'
                            });
                        } else {
                            instance.input.value = `${formatDate(start)} — ${formatDate(end)}`;
                            delete instance._selectedSingleDate;
                        }
                    }
                    validateSearchFields();
                },
                onOpen: function(selectedDates, dateStr, instance) {
                    if (instance._selectedSingleDate) {
                        instance.setDate([instance._selectedSingleDate], false);
                    }
                },
                onClose: function(selectedDates, dateStr, instance) {
                    if (selectedDates.length === 1) {
                        instance.setDate([selectedDates[0], selectedDates[0]], false);
                        setTimeout(() => {
                            instance.setDate([selectedDates[0]], false);
                            instance.input.value = formatDate(selectedDates[0]);
                        }, 10);
                    }
                }
            });
            if (preSelectedDates.length === 2) {
                $('#datePicker').val(`${formatDate(preSelectedDates[0])} — ${formatDate(preSelectedDates[1])}`);
            }
        }

        function handleCategoryChange() {
            $('.filter_wrapper .input_wrapper .search').prop("disabled", false);
            const category_selected = $('.parent input[name="category_id"]:checked').val();
            $('.members_wrapper').hide();
            $('.members_wrapper input').val('0').attr('disabled', true);

            if (category_selected == '1') {
                // Experiences
                $('#guests .guests').text(@json(translate('home.participants')));
                $('.members_wrapper.exp').show().find('input').attr('disabled', false);
                $('.location').text(@json(translate('home.where')));
                $('[name="address"]').attr('placeholder', @json(translate('home.enter_destination')));
                showRangeDatePicker(true);
                $('.members_wrapper input[name="children"]').val('0');

            } else if (category_selected == '2') {
                // Watercraft
                $('#guests .guests').text(@json(translate('home.passengers')));
                $('.members_wrapper.watercraft').show().find('input').attr('disabled', false);
                $('.location').text(@json(translate('home.where')));
                $('[name="address"]').attr('placeholder', @json(translate('home.enter_destination')));
                showRangeDatePicker(true);

            } else if (category_selected == '3') {
                // Vehicle
                $('#guests .guests').text(@json(translate('home.time')));
                $('.location').text(@json(translate('home.pick_up')));
                $('.members_wrapper.car').show().find('input').attr('disabled', false).val('');
                $('[name="address"]').attr('placeholder', @json(translate('home.airport_or_city')));
                showRangeDatePicker(true);

            } else if (category_selected == '4') {
                // Accommodation
                $('#guests .guests').text('Guests');
                $('.members_wrapper.acc').show().find('input').attr('disabled', false);
                $('.location').text(@json(translate('home.where')));
                $('[name="address"]').attr('placeholder', @json(translate('home.enter_destination')));
                showSingleDatePicker();
                $('.members_wrapper input[name="acc_children"]').val('0');
            }

            if (category_selected == '1' || category_selected == '2' || category_selected == '4') {
                $('.input_wrapper .guest_btn .number_guests').text(@json(translate('home.how_many')));
            } else {
                $('.input_wrapper .guest_btn .number_guests').text(@json(translate('home.pick_up_and_drop_off')));
            }

            if (category_selected == '1' || category_selected == '2' || category_selected == '3') {
                $('.date_input_wrapper .datePicker_label').html('<span class="w-75 fs-14">{{ translate('home.select_date') }}</span>')
            }

            $('.input_wrapper .guest_btn span').removeClass('guest_changed');
        }
        $(document).ready(function() {
            const data = loadUrlParameters();
            if (data.adults != null || data.children != null) {
                totalGuests = (parseInt(data.adults) || 0) + (parseInt(data.children) || 0);
            } else if (data.guest) {
                totalGuests = parseInt(data.guest) || 0;
            }
            if (totalGuests > 0) {
                $('.input_wrapper .guest_btn .number_guests').text(totalGuests + ' ');
                $('.input_wrapper .guest_btn span').addClass('guest_changed');
            }else{
                $('.input_wrapper .guest_btn .number_guests').text(@json(translate('home.how_many')));
                $('.input_wrapper .guest_btn span').removeClass('guest_changed');
            }
            handleCategoryChange();
            updateTotalGuests(data);
            $('#listing-loading').show();
            const urlParams = new URLSearchParams(window.location.search);
            let search_data_obj = {};

            // Check if we're on a location search page
            const currentPath = window.location.pathname;
            const isLocationSearch = currentPath.startsWith('/search/');

            if (isLocationSearch) {
                // Extract location slug from URL path
                const locationSlug = currentPath.replace('/search/', '');

                // Try to get original address from sessionStorage first
                let locationName = sessionStorage.getItem('original_address_' + locationSlug);

                // If not found, convert slug back to readable location name
                if (!locationName) {
                    locationName = locationSlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                }

                // Add address to search data
                search_data_obj['address'] = locationName;
            }

            const keys = ['date_ranges', 'lat', 'lng', 'category_id', 'adults', 'children', 'passenger', 'guest', 'pickUp', 'dropOff'];
            keys.forEach(key => {
                const value = urlParams.get(key);
                if (value !== null && value !== '') {
                    search_data_obj[key] = value;
                }
            });
            let search_data = $.param(search_data_obj);
            get_listing(search_data);
            $('.topbar .filter_btn_wrapper ').addClass('hidden');
            $('.show_map_sectiom ').addClass('hidden');
            $("#view_map").on("click", function() {
                $("#listing_map").toggle();
                let btn_text = $(this);
                if (btn_text.html() == "Map View") {
                    btn_text.html(@json(translate('home.list_view')));
                } else {
                    btn_text.html(@json(translate('home.map_view')));
                }
            });
            // location google map
            // google map location
            $(window).keydown(function(event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });

            function centerAutocompleteDropdown() {
                // const input = document.getElementById('autocomplete');
                // const pacContainers = document.getElementsByClassName('pac-container');

                // if (!input || pacContainers.length === 0) return;

                // const rect = input.getBoundingClientRect();
                // const screenWidth = window.innerWidth;

                // const inputLeft = rect.left;
                // const inputRight = rect.right;
                // const inputWidth = rect.width;
                // const inputCenter = inputLeft + inputWidth / 2;

                // for (let pac of pacContainers) {
                //     // Reset styles to prevent carry-over effects
                //     pac.style.position = 'absolute';
                //     pac.style.minWidth = `${inputWidth}px`;
                //     pac.style.zIndex = '1050';
                //     pac.style.left = '';
                //     pac.style.right = '';
                //     pac.style.transform = '';

                //     // Temporarily apply centered position to measure true width
                //     pac.style.left = `${inputCenter}px`;
                //     pac.style.transform = 'translateX(-50%)';

                //     // Ensure layout is updated
                //     const pacRect = pac.getBoundingClientRect();
                //     const pacWidth = pacRect.width;

                //     // Check overflow again after setting
                //     if (pacRect.left < 0) {
                //         // Overflowing to the left, align left
                //         pac.style.left = `${inputLeft}px`;
                //         pac.style.transform = 'none';
                //     } else if (pacRect.right > screenWidth) {
                //         // Overflowing to the right, align right
                //         const rightAlignedLeft = inputRight - pacWidth;
                //         pac.style.left = `${rightAlignedLeft}px`;
                //         pac.style.transform = 'none';
                //     }
                // }
                const input = document.getElementById('autocomplete');
                const pacContainers = document.getElementsByClassName('pac-container');

                if (!input || pacContainers.length === 0) return;

                const rect = input.getBoundingClientRect();
                const inputLeft = rect.left - 30;
                const inputWidth = rect.width;

                for (let pac of pacContainers) {
                    pac.style.position = 'absolute';
                    pac.style.minWidth = `${inputWidth}px`;
                    pac.style.zIndex = '1050';
                    pac.style.left = `${inputLeft}px`;
                    pac.style.transform = 'none';
                }
            }

            $(document).on('keyup keydown input', '#autocomplete', function() {
                centerAutocompleteDropdown();
            });

            // Add this CSS to prevent translation of Google Places dropdown
            const style = document.createElement('style');
            style.textContent = `
                .pac-container,
                .pac-container *,
                .pac-item,
                .pac-item-query,
                .pac-matched {
                    font-family: inherit !important;
                }
            `;
            document.head.appendChild(style);

            // Add notranslate class to autocomplete dropdown
            function preventAutocompleteTranslation() {
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1 && node.classList && node.classList.contains('pac-container')) {
                                node.classList.add('notranslate');
                                node.setAttribute('translate', 'no');
                                
                                // Also add to all child elements
                                const items = node.querySelectorAll('.pac-item, .pac-item-query, .pac-matched');
                                items.forEach(function(item) {
                                    item.classList.add('notranslate');
                                    item.setAttribute('translate', 'no');
                                });
                            }
                        });
                    });
                });
                
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            }

            // function initialize() {
            //     // Read URL parameters
            //     const urlParams = new URLSearchParams(window.location.search);
            //     const urlLat = parseFloat(urlParams.get('lat'));
            //     const urlLng = parseFloat(urlParams.get('lng'));

            //     // Use URL lat/lng if available, else use default
            //     const initialLat = !isNaN(urlLat) ? urlLat : {{ $listing->lat ?? 24.8871906 }};
            //     const initialLng = !isNaN(urlLng) ? urlLng : {{ $listing->lng ?? 67.1337822 }};

            //     var mapOptions = {
            //         center: new google.maps.LatLng(initialLat, initialLng),
            //         zoom: 13,
            //         mapTypeId: google.maps.MapTypeId.ROADMAP
            //     };
            //     var map = new google.maps.Map(document.getElementById('map_canvas'),
            //         mapOptions);
            //     var input = document.getElementById('autocomplete');
            //     var autocomplete = new google.maps.places.Autocomplete(input);
            //     autocomplete.bindTo('bounds', map);

            //     const observer = new MutationObserver(centerAutocompleteDropdown);
            //     observer.observe(document.body, {
            //         childList: true,
            //         subtree: true
            //     });

            //     var infowindow = new google.maps.InfoWindow();
            //     var marker = new google.maps.Marker({
            //         map: map
            //     });
            //     google.maps.event.addListener(autocomplete, 'place_changed', function() {
            //         infowindow.close();
            //         var place = autocomplete.getPlace();
            //         if (place.geometry.viewport) {
            //             map.fitBounds(place.geometry.viewport);
            //         } else {
            //             map.setCenter(place.geometry.location);
            //             map.setZoom(17); // Why 17? Because it looks good.
            //         }
            //         var image = new google.maps.MarkerImage(
            //             place.icon,
            //             new google.maps.Size(71, 71),
            //             new google.maps.Point(0, 0),
            //             new google.maps.Point(17, 34),
            //             new google.maps.Size(35, 35));
            //         marker.setIcon(image);
            //         marker.setPosition(place.geometry.location);
            //         var address = '';
            //         if (place.address_components) {
            //             address = [(place.address_components[0] && place.address_components[0].short_name ||
            //                 ''), (place.address_components[1] && place.address_components[1]
            //                 .short_name || ''), (place.address_components[2] && place
            //                 .address_components[2].short_name || '')].join(' ');
            //         }
            //         updateTextFields(place.geometry.location.lat(), place.geometry.location.lng());
            //         infowindow.setContent('<div><strong>' + place.name + '</strong><br>' + address +
            //             "<br>" + place.geometry.location);
            //         infowindow.open(map, marker);
            //     });
            //     // Sets a listener on a radio button to change the filter type on Places
            //     // Autocomplete.
            //     function setupClickListener(id, types) {
            //         var radioButton = document.getElementById(id);
            //         if (radioButton) {
            //             google.maps.event.addDomListener(radioButton, 'click', function() {
            //                 autocomplete.setTypes(types);
            //             });
            //         }
            //     }
            //     setupClickListener('changetype-all', []);
            //     setupClickListener('changetype-establishment', ['establishment']);
            //     setupClickListener('changetype-geocode', ['geocode']);
            // }
            function initialize() {
                // Read URL parameters
                const urlParams = new URLSearchParams(window.location.search);
                const urlLat = parseFloat(urlParams.get('lat'));
                const urlLng = parseFloat(urlParams.get('lng'));

                // Use URL lat/lng if available, else use default
                const initialLat = !isNaN(urlLat) ? urlLat : {{ $listing->lat ?? 24.8871906 }};
                const initialLng = !isNaN(urlLng) ? urlLng : {{ $listing->lng ?? 67.1337822 }};

                var mapOptions = {
                    center: new google.maps.LatLng(initialLat, initialLng),
                    zoom: 13,
                    mapTypeId: google.maps.MapTypeId.ROADMAP
                };
                var map = new google.maps.Map(document.getElementById('map_canvas'), mapOptions);
                var input = document.getElementById('autocomplete');
                var autocomplete = new google.maps.places.Autocomplete(input);
                autocomplete.bindTo('bounds', map);

                const observer = new MutationObserver(centerAutocompleteDropdown);
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                var infowindow = new google.maps.InfoWindow();
                var marker = new google.maps.Marker({
                    map: map
                });

                // Prevent translation of autocomplete
                preventAutocompleteTranslation();
                
                // Also add listener for when autocomplete opens
                input.addEventListener('focus', function() {
                    setTimeout(function() {
                        const pacContainer = document.querySelector('.pac-container');
                        if (pacContainer) {
                            pacContainer.classList.add('notranslate');
                            pacContainer.setAttribute('translate', 'no');
                        }
                    }, 100);
                });

                // **NEW CODE: If we have coordinates from URL, reverse geocode to get address**
                // Only do reverse geocoding if we don't already have a location name from URL
                if (!isNaN(urlLat) && !isNaN(urlLng) && input.value === '') {
                    var geocoder = new google.maps.Geocoder();
                    var latlng = new google.maps.LatLng(urlLat, urlLng);

                    geocoder.geocode({
                        'location': latlng
                    }, function(results, status) {
                        if (status === 'OK') {
                            if (results[0]) {
                                // Only set if input is still empty (not overridden by location search logic)
                                if (input.value === '') {
                                    // Set the input field with the formatted address
                                    input.value = results[0].formatted_address;

                                    // Show remove button since input now has value
                                    $(input).closest('.input_wrapper').find('.remove_btn').addClass('show');

                                    console.log('Address set from coordinates:', results[0].formatted_address);
                                }

                                // Always update the hidden lat/lng fields and marker
                                updateTextFields(urlLat, urlLng);
                                marker.setPosition(latlng);
                            } else {
                                console.log('No results found for reverse geocoding');
                            }
                        } else {
                            console.log('Geocoder failed due to: ' + status);
                        }
                    });
                } else if (!isNaN(urlLat) && !isNaN(urlLng)) {
                    // If we have coordinates but already have location name, just update coordinates and marker
                    updateTextFields(urlLat, urlLng);
                    var latlng = new google.maps.LatLng(urlLat, urlLng);
                    marker.setPosition(latlng);
                    $(input).closest('.input_wrapper').find('.remove_btn').addClass('show');
                }

                google.maps.event.addListener(autocomplete, 'place_changed', function() {
                    infowindow.close();
                    var place = autocomplete.getPlace();
                    if (place.geometry.viewport) {
                        map.fitBounds(place.geometry.viewport);
                    } else {
                        map.setCenter(place.geometry.location);
                        map.setZoom(17);
                    }
                    var image = new google.maps.MarkerImage(
                        place.icon,
                        new google.maps.Size(71, 71),
                        new google.maps.Point(0, 0),
                        new google.maps.Point(17, 34),
                        new google.maps.Size(35, 35));
                    marker.setIcon(image);
                    marker.setPosition(place.geometry.location);
                    var address = '';
                    if (place.address_components) {
                        address = [(place.address_components[0] && place.address_components[0].short_name ||
                            ''), (place.address_components[1] && place.address_components[1]
                            .short_name || ''), (place.address_components[2] && place
                            .address_components[2].short_name || '')].join(' ');
                    }
                    updateTextFields(place.geometry.location.lat(), place.geometry.location.lng());
                    infowindow.setContent('<div><strong>' + place.name + '</strong><br>' + address +
                        "<br>" + place.geometry.location);
                    infowindow.open(map, marker);
                });

                // Sets a listener on a radio button to change the filter type on Places Autocomplete.
                function setupClickListener(id, types) {
                    var radioButton = document.getElementById(id);
                    if (radioButton) {
                        google.maps.event.addDomListener(radioButton, 'click', function() {
                            autocomplete.setTypes(types);
                        });
                    }
                }
                setupClickListener('changetype-all', []);
                setupClickListener('changetype-establishment', ['establishment']);
                setupClickListener('changetype-geocode', ['geocode']);
            }
            google.maps.event.addDomListener(window, 'load', initialize);

            function updateTextFields(lat, lng) {
                $('#lat').val(lat);
                $('#lng').val(lng);
                validateSearchFields();
            }

            $(".select_multi").select2({
                closeOnSelect: true,
                placeholder: @json(translate('home.select_amenities')),
                multiple: true,
                // allowClear: true,
                // tags: true
            });

            $('.dropdown-menu').on('click', function(event) {
                event.stopPropagation();
            });

            // wishlist
            $(document).on("click", ".heart", function() {
                let listing_id = $(this).val();
                $.ajax({
                    url: `{{ route('add_wishlist') }}`,
                    method: "POST",
                    data: {
                        "_token": "{{ csrf_token() }}",
                        listing_id
                    },
                    success: function(response) {
                        $("#wishlist_count").html(response.data);
                        $("#wishlist_count").attr('data-count', response.data);
                        console.log(response);
                    }
                })
            })
            $(".advance_filter").submit(function(e) {
                e.preventDefault();
                let advance_filer = $(this).serialize();
                get_listing(advance_filer);
            })

            // On Apply button click in the modal
            $(".adv_apply").click(function(e) {
                e.preventDefault();
                const selectedValue = $('.topbar .top_box1 .box [name="category_id"]:checked').val();
                const modalFilterIdMap = {
                    1: "#tour_filter_category",
                    2: "#boat_filter_category",
                    3: "#car_filter_category",
                    4: "#filter_category",
                };
                const modalFilterData = selectedValue in modalFilterIdMap ?
                    $(modalFilterIdMap[selectedValue]).find(":input").serialize() :
                    "";
                const searchFormData = $("#search-box").find(":input").serialize();
                const combinedFilterData = `${searchFormData}&${modalFilterData}`;
                get_listing(combinedFilterData);
                if (selectedValue in modalFilterIdMap) {
                    $(modalFilterIdMap[selectedValue]).modal('hide');
                }
            });

            $('.footer_category').click(function(e) {
                e.preventDefault();
                const $footer_category = $(this).attr('data-category');
                window.scrollTo(0, 0);
                setTimeout(() => {
                    get_listing({
                        category_id: $footer_category
                    });
                }, 500);
            });

            // get listing
            let allListings = [];
            let listingsRendered = 0;
            const listingsPerPage = 8;

            function renderListingsChunk() {
                const end = listingsRendered + listingsPerPage;
                const chunk = allListings.slice(listingsRendered, end);
                listingsRendered = end;

                $(".more_listing_spinner").html(
                    '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true" style="color: #ffce32"></span>'
                );

                setTimeout(() => {
                    chunk.forEach(item => {
                        const $item = $(item);
                        $('#listing_row').append($item); // append the jQuery element, not raw HTML

                        $item.find('.productSwiper').each(function() {
                            const swiperEl = this;
                            new Swiper(swiperEl, {
                                slidesPerView: 1,
                                spaceBetween: 15,
                                lazy: true,
                                pagination: {
                                    el: $(swiperEl).find(".swiper-pagination")[0],
                                    clickable: true,
                                    dynamicBullets: true,
                                },
                                navigation: {
                                    nextEl: $(swiperEl).find(".swiper-button-next")[
                                        0],
                                    prevEl: $(swiperEl).find(".swiper-button-prev")[
                                        0],
                                },
                                loop: true,
                                centeredSlides: true,
                            });
                        });
                    });

                    if (listingsRendered >= allListings.length) {
                        $(".more_listing_spinner").html('');
                        $(".more_listing_spinner").hide();
                    } else {
                        $(".more_listing_spinner").html('');
                        $(".more_listing_spinner").show();
                    }

                }, 50);
            }

            function get_listing(search_data) {
                $('#listing-loading').show();
                var category_id = $('input[name="category_id"]:checked').val();

                if (typeof search_data === 'object') {
                    search_data = $.param(search_data);
                }

                if (!search_data) {
                    const urlParams = new URLSearchParams(window.location.search);
                    search_data = urlParams.get('search_data') || '';
                }
                $.ajax({
                    url: "{{ route('get_listing') }}",
                    data: {
                        listing_name: "{{ request()->listing_name }}",
                        search_data: search_data
                    },
                    type: "GET",
                    success: function(response) {
                        console.log(response);
                        $("#listing_row").empty();
                        if (response.status === true) {
                            allListings = [];
                            listingsRendered = 0;
                            // Assume response.data contains HTML strings for listings
                            // Wrap it in a jQuery object to split individual items
                            const tempWrapper = $('<div>').html(response.data);
                            tempWrapper.children().each(function() {
                                allListings.push($(this).prop('outerHTML'));
                            });

                            renderListingsChunk(); // Initial 8 listings

                            initialize1(response.json_data); // keep this as is

                            // new Swiper(".productSwiper", {
                            //     slidesPerView: 1,
                            //     spaceBetween: 15,
                            //     lazy: true,
                            //     pagination: {
                            //         el: ".swiper-pagination",
                            //         clickable: true,
                            //         dynamicBullets: true,
                            //     },
                            //     navigation: {
                            //         nextEl: ".swiper-button-next",
                            //         prevEl: ".swiper-button-prev",
                            //     },
                            //     loop: true,
                            //     centeredSlides: true,
                            // });

                        } else {
                            $(".Load_more").hide();
                            $("#listing_row").html(
                                `<div class="empty_listing">${response.message}</div>`);
                            $('#card-view').parent().addClass('col-md-12').removeClass('col-md-8');
                        }
                    },
                    complete: function() {
                        $('#listing-loading').hide();
                    }
                });
            }


            $(window).on('scroll', function() {
                if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                    renderListingsChunk();
                }
            });

            // reset filter
            $(".reset_btn").on("click", function() {
                $("#listing_row").empty();
                $("#datePicker").val('');
                get_listing();

                $('.topbar .filter_btn_wrapper ').addClass('hidden');
                $('.show_map_sectiom ').addClass('hidden');

                $('.topbar .top_box1 .box>input:checked').each(function() {
                    $(this).prop('checked', false);
                });
                $('.topbar .top_box2 .search_input').val('');
                $('#guests').val('');
                $('#card-view').parent().addClass('col-md-12').removeClass('col-md-8');
                $('.filter_wrapper .input_wrapper .search').prop("disabled", true);
            });

            // search
            // Updated search form submission
            $("#search-box").on("submit", function(e) {
                e.preventDefault();
                const lat = $('#lat').val();
                const lng = $('#lng').val();
                const address = $('#autocomplete').val();
                const category_selected = $('.parent input[name="category_id"]:checked').val();
                const dateRanges = $('.date_ranges').val();

                // Create location slug from address
                function createLocationSlug(locationName) {
                    if (!locationName || locationName.trim() === '') {
                        return 'all-locations';
                    }

                    return locationName.toLowerCase()
                        .trim()
                        // Replace spaces with hyphens first
                        .replace(/\s+/g, '-')
                        // Remove special characters except hyphens and alphanumeric
                        .replace(/[^a-z0-9-]/g, '')
                        // Clean up multiple consecutive hyphens
                        .replace(/-+/g, '-')
                        // Remove leading and trailing hyphens
                        .replace(/^-|-$/g, '');
                }

                const locationSlug = createLocationSlug(address);

                // Store original address in sessionStorage for later retrieval
                if (address && address.trim() !== '') {
                    sessionStorage.setItem('original_address_' + locationSlug, address);
                }

                let searchParams = {};

                if (category_selected == 1) {
                    const adult = parseInt($('.members_wrapper .guest_wrapper #adults').val());
                    const child = parseInt($('.members_wrapper .guest_wrapper #children').val());
                    searchParams = {
                        date_ranges: dateRanges,
                        adults: adult,
                        children: child,
                        lng: lng,
                        lat: lat,
                        category_id: category_selected
                    };
                } else if (category_selected == 2) {
                    const passenger = parseInt($('.members_wrapper .guest_wrapper #passenger').val());
                    searchParams = {
                        date_ranges: dateRanges,
                        passenger: passenger,
                        lng: lng,
                        lat: lat,
                        category_id: category_selected
                    };
                } else if (category_selected == 3) {
                    var pickUp = $('.members_wrapper.car .pick_up_time').val() || "";
                    var dropOff = $('.members_wrapper.car .drop_off_time').val() || "";
                    searchParams = {
                        date_ranges: dateRanges,
                        pickUp: pickUp,
                        dropOff: dropOff,
                        lng: lng,
                        lat: lat,
                        category_id: category_selected
                    };
                } else if (category_selected == 4) {
                    const guest = parseInt($('.members_wrapper.acc #guest').val());
                    const children = parseInt($('.members_wrapper.acc #acc_children').val());
                    searchParams = {
                        date_ranges: dateRanges,
                        guest: guest,
                        children: children,
                        lng: lng,
                        lat: lat,
                        category_id: category_selected
                    };
                }

                // Build query string for the clean URL
                const queryString = new URLSearchParams();
                for (const key in searchParams) {
                    if (searchParams[key] !== null && searchParams[key] !== undefined && searchParams[key] !== '') {
                        queryString.set(key, searchParams[key]);
                    }
                }

                // Update URL without page reload using History API
                const cleanUrl = `/search/${locationSlug}${queryString.toString() ? '?' + queryString.toString() : ''}`;
                window.history.pushState({}, '', cleanUrl);

                // Continue with existing AJAX search functionality
                $("#listing_row").empty();
                let search_data = $(this).serialize();
                console.log(search_data);
                get_listing(search_data);
                $("#view_map").show();
                $('#card-view').parent().addClass('col-md-8').removeClass('col-md-12');
                $('.topbar .filter_btn_wrapper ').removeClass('hidden');
                $('.show_map_sectiom ').removeClass('hidden');
                $('.guest_btn, .members_dropdown').removeClass('show');
                $('.guest_btn ').attr('aria-expanded', false);
            });

            // Single Date Picker
            // $('input[name="dateExp"]').daterangepicker({
            //     singleDatePicker: true,
            //     autoUpdateInput: false,
            //     opens: 'center',
            //     minDate: moment(),
            //     locale: {
            //         format: 'YYYY-MM-DD',
            //         cancelLabel: 'Clear'
            //     },
            //     autoApply: true,
            //     startDate: moment(),
            // }).on('apply.daterangepicker', function(ev, picker) {
            //     $(this).val(picker.startDate.format('YYYY-MM-DD'));
            // });

            // Date Range Picker
            // $('input[name="daterange"]').daterangepicker({
            //     autoUpdateInput: false,
            //     opens: 'center',
            //     minDate: new Date(),
            //     autoApply: true,
            //     linkedCalendars: true,
            // }, function(start, end, label) {
            //     console.log("A new date selection was made: " + start.format('YYYY-MM-DD') + ' to ' + end
            //         .format('yy/mm/dd'));
            // });

            $('input[name="daterange"]').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('DD-MMM-YYYY') + ' - ' + picker.endDate.format(
                    'DD-MMM-YYYY'));
                $('.date_wrapper').find('.remove_btn').addClass('show');
            });

            $('input[name="dateExp"]').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('DD-MMM-YYYY'));
                $('.date_wrapper').find('.remove_btn').addClass('show');
            });

            var $range = $(".js-range-slider"),
                $from = $(".from"),
                $to = $(".to"),
                range,
                min = $range.data('min'),
                max = $range.data('max'),
                from,
                to;

            var updateValues = function() {
                $from.prop("value", from);
                $to.prop("value", to);
            };

            $range.ionRangeSlider({
                prefix: "$",
                postfix: " COP",
                onChange: function(data) {
                    from = data.from;
                    to = data.to;
                    updateValues();
                }
            });

            range = $range.data("ionRangeSlider");

            var updateRange = function() {
                range.update({
                    from: from,
                    to: to
                });
            };

            function resetSlider() {
                ranges = $range.data("ionRangeSlider");

                ranges.update({
                    from: min,
                    to: max
                });
            }

            // Reset Filter
            $('.adv_reset').on('click', function() {
                var filter_parent = $(this).closest('.modal-content').find('.modal-body');

                filter_parent.find('[type="radio"], [type="checkbox"]').prop('checked', false);
                filter_parent.find('[type="number"]').val('1');
                filter_parent.find('[type="text"]').val('');

                filter_parent.find('.js-example-basic-multiple').each(function() {
                    $(this).val(null).trigger('change');
                });
                resetSlider();
            });

            // map view google map
            function initialize1(data) {
                // Log initial data
                console.log('Initializing map with data:', data);

                // Convert object to array if necessary and filter out null/undefined entries
                let location = (Array.isArray(data) ? data : Object.values(data)).filter(item => item != null);
                console.log('Location after conversion:', location, 'Is array:', Array.isArray(location), 'Length:',
                    location.length);

                // Initialize the map
                var mapContainer = document.getElementById('listing_map');
                if (!mapContainer) {
                    console.error('Map container "listing_map" not found!');
                    return;
                }

                var map = new google.maps.Map(mapContainer, {
                    zoom: 17,
                    center: new google.maps.LatLng(parseFloat($('#lat').val()) || 10.446315, parseFloat($(
                        '#lng').val()) || -75.516454),
                    mapTypeId: google.maps.MapTypeId.ROADMAP
                });
                console.log('Map initialized with center:', map.getCenter().toString());

                var bounds = new google.maps.LatLngBounds();
                var markers = [];

                // Function to create marker icon
                function createMarkerIcon(number, height, borderRadius, strokeColor, strokeWidth, fillColor) {
                    var covertedNumber = parseFloat(number.toString().replace(/[^0-9.]/g, '')) || 0;
                    var text = `$${covertedNumber.toLocaleString('en-US')} COP`;

                    var tempCanvas = document.createElement('canvas');
                    var tempContext = tempCanvas.getContext('2d');
                    tempContext.font = '600 14px Poppins';
                    var textWidth = tempContext.measureText(text).width;

                    var padding = 10;
                    var width = textWidth + padding * 2;

                    var canvas = document.createElement('canvas');
                    var context = canvas.getContext('2d');
                    canvas.width = width;
                    canvas.height = height;

                    context.beginPath();
                    context.moveTo(borderRadius + strokeWidth / 2, strokeWidth / 2);
                    context.lineTo(width - borderRadius - strokeWidth / 2, strokeWidth / 2);
                    context.quadraticCurveTo(width - strokeWidth / 2, strokeWidth / 2, width - strokeWidth / 2,
                        borderRadius + strokeWidth / 2);
                    context.lineTo(width - strokeWidth / 2, height - borderRadius - strokeWidth / 2);
                    context.quadraticCurveTo(width - strokeWidth / 2, height - strokeWidth / 2, width -
                        borderRadius - strokeWidth / 2, height - strokeWidth / 2);
                    context.lineTo(borderRadius + strokeWidth / 2, height - strokeWidth / 2);
                    context.quadraticCurveTo(strokeWidth / 2, height - strokeWidth / 2, strokeWidth / 2, height -
                        borderRadius - strokeWidth / 2);
                    context.lineTo(strokeWidth / 2, borderRadius + strokeWidth / 2);
                    context.quadraticCurveTo(strokeWidth / 2, strokeWidth / 2, borderRadius + strokeWidth / 2,
                        strokeWidth / 2);
                    context.closePath();

                    context.fillStyle = fillColor;
                    context.fill();
                    context.strokeStyle = strokeColor;
                    context.lineWidth = strokeWidth;
                    context.stroke();

                    context.font = '600 14px Poppins';
                    context.fillStyle = '#000000';
                    context.textAlign = 'center';
                    context.textBaseline = 'middle';
                    context.fillText(text, width / 2, height / 2);

                    var icon = {
                        url: canvas.toDataURL(),
                        scaledSize: new google.maps.Size(width, height),
                        origin: new google.maps.Point(0, 0),
                        anchor: new google.maps.Point(width / 2, height / 2)
                    };
                    console.log('Marker icon created for price:', text);
                    return icon;
                }

                // Array to keep track of open info windows
                var openInfoWindows = [];

                // Process each location
                for (var i = 0; i < location.length; i++) {
                    if (!location[i] || typeof location[i] !== 'object') {
                        console.warn(`Skipping invalid listing at index ${i}:`, location[i]);
                        continue;
                    }
                    console.log(`Processing listing ${i}:`, location[i]);

                    let price = "Dr";
                    if (location[i].detail && typeof location[i].detail === 'object' && location[i].detail
                        .basis_type) {
                        if (location[i].detail.basis_type === 'Hourly') {
                            price = location[i].detail.per_hour;
                        } else if (location[i].detail.basis_type === 'Daily') {
                            price = location[i].detail.per_day;
                        } else {
                            price = location[i].detail.adult_price;
                        }
                        price = price ? `$${price}` : "Dr";
                    }
                    console.log(`Listing ${i}: Name=${location[i].name || 'Unknown'}, Price=${price}`);

                    // Convert lat/lng to numbers
                    var lat = parseFloat(location[i].lat);
                    var lng = parseFloat(location[i].lng);
                    if (isNaN(lat) || isNaN(lng)) {
                        console.warn(
                            `Invalid coordinates for listing ${i}: lat=${location[i].lat}, lng=${location[i].lng}`
                        );
                        continue;
                    }

                    var markerPosition = new google.maps.LatLng(lat, lng);
                    bounds.extend(markerPosition);
                    console.log(`Marker ${i}: Lat=${lat}, Lng=${lng}, Position=${markerPosition.toString()}`);

                    var marker = new google.maps.Marker({
                        position: markerPosition,
                        map: map,
                        title: location[i].name || 'Unknown',
                        icon: createMarkerIcon(price, 34, 18, '#FFD324', 3, '#ffffff')
                    });
                    markers.push(marker);
                    console.log(`Marker ${i} created and added to map`);

                    let swiperSlidesHtml = '';
                    if (Array.isArray(location[i].gallery_images)) {
                        location[i].gallery_images.forEach((imgObj) => {
                            swiperSlidesHtml += `
                                <div class="swiper-slide">
                                    <div class="slide-img">
                                        <img src="{{ asset('website') }}/${imgObj.url || ''}"
                                            alt="${imgObj.type || 'image'}"
                                            loading="lazy"
                                            onerror="this.onerror=null;this.src='{{ asset('website/images/plcaeholderListingImg.png') }}';">
                                    </div>
                                </div>`;
                        });
                    }

                    google.maps.event.addListener(marker, 'click', (function(marker, i) {
                        return function() {
                            console.log(`Marker ${i} clicked`);
                            var newFillColor = '#FFD324';
                            var newIcon = createMarkerIcon(price, 34, 18, '#FFD324', 3,
                                newFillColor);
                            marker.setIcon(newIcon);

                            if (openInfoWindows[i]) {
                                openInfoWindows[i].close();
                                var originalIcon = createMarkerIcon(price, 34, 18, '#FFD324', 3,
                                    '#ffffff');
                                marker.setIcon(originalIcon);
                                openInfoWindows[i] = null;
                                console.log(`Info window for marker ${i} closed`);
                            } else {
                                var contentString = `
                                    <div class="product map_product">
                                        <div class="col-12">
                                            <div class="product_box mb-0">
                                                <a href="{{ url('detail') }}/${location[i].ids || ''}/${location[i].slug || ''}" target="_blank">
                                                    <div class="product_img">
                                                        <div class="product_wish d-flex align-items-center justify-content-between px-3">
                                                            <div class="client">
                                                                <img src="{{ asset('website') . '/' }}${location[i].user?.avatar || ''}">
                                                            </div>
                                                            ${location[i].wishlist != null ?
                                `<input type="checkbox" class="heart d-none" value="${location[i].id || ''}" id="wishlist_${location[i].id || i}" checked>
                                                                                                                                                                                                                                                                                    <label for="wishlist_${location[i].id || i}">
                                                                                                                                                                                                                                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                                                                                                                                                                                                                                                            <path d="M12.62 20.81C12.28 20.93 11.72 20.93 11.38 20.81C8.48 19.82 2 15.69 2 8.69001C2 5.60001 4.49 3.10001 7.56 3.10001C9.38 3.10001 10.99 3.98001 12 5.34001C12.5138 4.64588 13.183 4.08173 13.954 3.69275C14.725 3.30377 15.5764 3.10077 16.44 3.10001C19.51 3.10001 22 5.60001 22 8.69001C22 15.69 15.52 19.82 12.62 20.81Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                                                                                                                                                                                                                                                                        </svg>
                                                                                                                                                                                                                                                                                    </label>` :
                                `<input type="checkbox" class="d-none" value="${location[i].id || ''}" id="wishlist_${location[i].id || i}">
                                                                                                                                                                                                                                                                                    <label for="wishlist_${location[i].id || i}" data-bs-toggle="modal" data-bs-target="#login">
                                                                                                                                                                                                                                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                                                                                                                                                                                                                                                            <path d="M12.62 20.81C12.28 20.93 11.72 20.93 11.38 20.81C8.48 19.82 2 15.69 2 8.69001C2 5.60001 4.49 3.10001 7.56 3.10001C9.38 3.10001 10.99 3.98001 12 5.34001C12.5138 4.64588 13.183 4.08173 13.954 3.69275C14.725 3.30377 15.5764 3.10077 16.44 3.10001C19.51 3.10001 22 5.60001 22 8.69001C22 15.69 15.52 19.82 12.62 20.81Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                                                                                                                                                                                                                                                                        </svg>
                                                                                                                                                                                                                                                                                    </label>`}
                                                        </div>
                                                        <div class="swiper productSwiperMap productSwiperHidden_${i}">
                                                            <div class="swiper-wrapper">
                                                                ${swiperSlidesHtml}
                                                            </div>
                                                            <div class="swiper-pagination"></div>
                                                            <div class="swiper-button-next"><i class="bi bi-chevron-right"></i></div>
                                                            <div class="swiper-button-prev"><i class="bi bi-chevron-left"></i></div>
                                                        </div>
                                                    </div>
                                                    <div class="rating" style="margin-bottom: 10px;">
                                                        <h5 class="" style="flex: 1; color: black; font-size: 16px; margin-bottom: 0;">${location[i].name || 'Unknown'}</h5>
                                                        <p class="star-rate" style="margin-bottom: 0;"><i class="fas fa-star" style="margin-right: 5px; color: #FFCE32;"></i>${location[i].rating == 0 ? 'New' : location[i].rating}</p>
                                                    </div>
                                                    <div class="description-wrapper">
                                                        <div class="description-content">
                                                            <p class="text-start">${location[i].description || ''}</p>
                                                        </div>
                                                        <a href="{{ url('detail') }}/${location[i].ids || ''}/${location[i].slug || ''}" class="show-more d-none" target="_blank">View more</a>
                                                    </div>
                                                    <span class="span-italic" style="margin-top: 15px; display: block; text-align: center;">
                                                        ${location[i].detail && location[i].detail.basis_type == "Hourly" ? (location[i].detail.per_hour || '') + @json(translate('home.per_hourly')) : ''}
                                                        ${location[i].detail && location[i].detail.basis_type == "Daily" ? (location[i].detail.per_day || '') + @json(translate('home.per_day')) : ''}
                                                        ${location[i].detail && location[i].detail.basis_type == null ? (location[i].detail.adult_price || '') + @json(translate('home.per_adult')) : ''}
                                                    </span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>`;
                                var infowindow = new google.maps.InfoWindow({
                                    content: contentString
                                });
                                infowindow.open(map, marker);
                                openInfoWindows[i] = infowindow;
                                console.log(`Info window opened for marker ${i}`);

                                google.maps.event.addListener(infowindow, 'closeclick', function() {
                                    var originalIcon = createMarkerIcon(price, 34, 18,
                                        '#FFD324', 3, '#ffffff');
                                    marker.setIcon(originalIcon);
                                    openInfoWindows[i] = null;
                                    console.log(`Info window for marker ${i} closed`);
                                });

                                setTimeout(() => {
                                    const wrapper = document.querySelector('.description-wrapper');
                                        if (wrapper) {
                                            const content = wrapper.querySelector('.description-content');
                                            const showMoreLink = wrapper.querySelector('.show-more');

                                            if (content && showMoreLink) {
                                                const originalStyle = content.style.webkitLineClamp;
                                                content.style.webkitLineClamp = 'unset';

                                                const isOverflowing = content.scrollHeight > content.offsetHeight + 5;
                                                content.style.webkitLineClamp = originalStyle;

                                                if (isOverflowing) {
                                                    showMoreLink.classList.remove('d-none');
                                                }
                                            }
                                        }
                                }, 500);

                                setTimeout(() => {
                                    var productSwiperHidden = new Swiper(
                                        `.productSwiperHidden_${i}`, {
                                            slidesPerView: 1,
                                            spaceBetween: 0,
                                            lazy: true,
                                            pagination: {
                                                el: ".swiper-pagination",
                                                clickable: true,
                                                dynamicBullets: true,
                                            },
                                            navigation: {
                                                nextEl: ".swiper-button-next",
                                                prevEl: ".swiper-button-prev",
                                            },
                                            loop: true,
                                            centeredSlides: true,
                                        });

                                }, 1500);
                            }
                        };
                    })(marker, i));
                }

                // Fit map to bounds or set center for single marker
                if (location.length > 1) {
                    console.log('Fitting bounds:', bounds.toString());
                    map.fitBounds(bounds, {
                        padding: 50
                    });
                    google.maps.event.addListenerOnce(map, 'bounds_changed', function() {
                        var zoom = map.getZoom();
                        console.log('Current zoom level:', zoom);
                        if (zoom > 18) {
                            map.setZoom(18);
                            console.log('Zoom capped at 18');
                        } else if (zoom < 10) {
                            map.setZoom(10);
                            console.log('Zoom set to minimum 10');
                        }
                    });
                } else if (location.length === 1) {
                    map.setCenter(bounds.getCenter());
                    map.setZoom(16);
                    console.log('Single marker: Centered at', bounds.getCenter().toString(), 'Zoom:', 16);
                } else {
                    console.warn('No valid listings to display on the map');
                }

                console.log('Total markers added:', markers.length);
                // Force map redraw
                google.maps.event.trigger(map, 'resize');
            }

            // Topbar background color transition starts from here
            const $highlight = $('<div class="highlight-bar"></div>').hide(); // initially hidden
            $('.topbar').prepend($highlight);

            function moveHighlight($target) {
                const offset = $target.position();
                const width = $target.outerWidth();
                const height = $target.outerHeight();

                $highlight.css({
                    left: offset.left,
                    top: offset.top,
                    width: width,
                    height: height
                }).fadeIn(200); // show with fade-in
            }

            // On click on column
            $(document).on('click', '.topbar .single_input_wrapper', function(e) {
                // e.stopPropagation(); // prevent click from bubbling to document
                $('.topbar .single_input_wrapper').removeClass('active');
                $(this).addClass('active');
                moveHighlight($(this));
            });

            // On click anywhere else, hide the highlight
            $(document).on('click', function(e) {
                // If click is not on or inside a .single_input_wrapper
                if (!$(e.target).closest('.single_input_wrapper').length) {
                    $('.topbar .single_input_wrapper').removeClass('active');
                    $highlight.fadeOut(200);
                }
            });

            // Set initial highlight if any active column is already marked
            // const $initial = $('.topbar .single_input_wrapper.active').length
            //     ? $('.topbar .single_input_wrapper.active')
            //     : $('.topbar .single_input_wrapper').first().addClass('active');

            // moveHighlight($initial);

        });
    </script>
    <script src="{{ asset('plugins/components/icheck/icheck.min.js') }}"></script>
    <script src="{{ asset('plugins/components/icheck/icheck.init.js') }}"></script>
    <script src="{{ asset('website/assets_cdn/flatpicker/flatpicker.js') }}"></script>
    {{-- amenities --}}
    <script>
        $(document).ready(function() {
            $('.js-example-basic-multiple').select2({
                placeholder: @json(translate('home.select_an_options')),
                allowClear: true,
            });
            // $('.parent input[name="category_id"]').change(function() {
            //     var category_selected = $('.parent input[name="category_id"]:checked').val();

            // });
            $('.members_wrapper .guest_wrapper button').on('click', function() {
                setTimeout(updateTotalGuests, 100); // wait for value to update
            });

            $('.duration input').on('change', function() {
                var tour_type = $('.duration input').val();

                if ($('.duration input[value="same_day"]').prop('checked')) {
                    $('.day_time').show();
                } else {
                    $('.day_time').hide();
                }
            });

        });

        // function rangePicker() {
        //     $('.date_wrapper .singlePicker').hide().attr('disabled', true);
        //     $('.date_wrapper .singlePicker input').val('');
        //     $('.date_wrapper .rangePicker').show().attr('disabled', false);
        //     $('.date_wrapper .date_input_wrapper').attr('for', 'datePicker');

        // }

        // function singlePicker() {
        //     $('.date_wrapper .rangePicker').hide().attr('disabled', true);
        //     $('.date_wrapper .rangePicker input').val('');
        //     $('.date_wrapper .singlePicker').show().attr('disabled', false);
        //     $('.date_wrapper .date_input_wrapper').attr('for', 'dateExp');

        // }

        // $('.remove_btn').on('click', function(e) {
        //     $(this).closest('.input_wrapper').find('input').val('');
        //     $(this).removeClass('show');
        // });

        $('.remove_btn').on('click', function(e) {
            e.preventDefault();
            const $wrapper = $(this).closest('.input_wrapper, .d-btn');
            $wrapper.find('input').each(function () {
                $(this).val('');

                if (this._flatpickr) {
                    this._flatpickr.clear();
                }
                if($wrapper.find('#autocomplete').length) {
                    $('#lat').val('');
                    $('#lng').val('');
                }
            });
            $(this).removeClass('show');
            validateSearchFields();
        });


        // Date Range picker customization starts from here

        $(document).ready(function() {
            $('.parent input[name="category_id"]').change(function() {
                handleCategoryChange();
            });

            $(document).on('click', function(e) {
                if (!$(e.target).closest('.guest_btn, .flatpickr-time').length) {
                    $('.guest_btn, .members_dropdown').removeClass('show');
                    $('.guest_btn').attr('aria-expanded', false);
                }
            });
            showRangeDatePicker(true);
            // function showSingleDatePicker() {
            //     $('#datePicker').val('').attr('disabled', true).hide();
            //     $('#dateExp').val('').attr('disabled', false).show();
            //     $('#dateExpEnd').val('').attr('disabled', false).show();

            //     // $('#dateExp').data('daterangepicker').remove();

            //     // Initialize the start datepicker
            //     flatpickr("#dateExp", {
            //         dateFormat: "d-M-Y",
            //         showMonths: 2, // Two months view
            //         // disableMobile: true;
            //         // position: 'auto center',
            //         minDate: 'today',
            //         onChange: function(selectedDates, dateStr, instance) {
            //             if (selectedDates.length > 0) {
            //                 // Set minimum date on end datepicker
            //                 endPicker.set('minDate', selectedDates[0]);
            //             }
            //         }
            //     });

            //     // Initialize the end datepicker separately so we can access it via variable
            //     const endPicker = flatpickr(".date-picker-end", {
            //         dateFormat: "d-M-Y",
            //         showMonths: 2 // Optional, if you also want 2-month view here
            //     });

            //     // Duplicate calendar for 2-month view
            //     setTimeout(() => {
            //         const $container = $('.daterangepicker.single .calendar');
            //         if ($container.length && $container.find('.calendar-table').length === 1) {
            //             const $clone = $container.clone();
            //             $container.after($clone);
            //             $('.daterangepicker.single .calendar').css({
            //                 display: 'flex',
            //                 justifyContent: 'space-between',
            //                 width: '800px'
            //             });
            //         }
            //     }, 100);
            // }


            function clearDateRange() {
                if (window.startPicker) {
                    window.startPicker.clear();
                }
                if (window.endPicker) {
                    window.endPicker.clear();
                    window.endPicker.set('minDate', 'today');
                }
            }



            validateSearchFields();
            const guestObserver = new MutationObserver(() => {
                validateSearchFields();
            });
            guestObserver.observe(document.querySelector('.number_guests'), { childList: true, subtree: true });



        });
        function showSingleDatePicker() {
                $('#datePicker').val('').attr('disabled', true).hide();
                $('#dateExp').val('').attr('disabled', false).show();
                $('#dateExpEnd').val('').attr('disabled', false).show();

                if (window.startPicker) {
                    window.startPicker.destroy();
                }
                if (window.endPicker) {
                    window.endPicker.destroy();
                }

                window.startPicker = flatpickr("#dateExp", {
                    dateFormat: "D, M j",
                    // altInput: true,
                    // altFormat: "D, M j",
                    showMonths: 2,
                    minDate: 'today',
                    position: "auto center",
                    onChange: function(selectedDates, dateStr, instance) {
                        if (selectedDates.length > 0) {
                            const startDate = selectedDates[0];

                            const nextDay = new Date(startDate);
                            nextDay.setDate(nextDay.getDate() + 1);

                            window.endPicker.set('minDate', nextDay);
                            if (window.endPicker.selectedDates.length > 0) {
                                const endDate = window.endPicker.selectedDates[0];
                                if (endDate <= startDate) {
                                    window.endPicker.clear();
                                }
                            }
                        }
                        validateSearchFields();
                    },
                    onClose: function(selectedDates, dateStr, instance) {
                        if (selectedDates.length === 1) {
                            console.log('Start date preserved:', dateStr);

                            setTimeout(() => {
                                window.endPicker.open();
                            }, 50);
                        }
                    }
                });

                window.endPicker = flatpickr("#dateExpEnd", {
                    dateFormat: "D, M j",
                    // altInput: true,
                    // altFormat: "D, M j",
                    showMonths: 2,
                    minDate: 'today',
                    position: "auto center",
                    onChange: function(selectedDates, dateStr, instance) {
                        if (selectedDates.length > 0) {
                            // console.log('End date selected:', dateStr);
                            validateDateRange();
                        }
                        validateSearchFields();
                    }
                });

                function validateDateRange() {
                    const startDate = window.startPicker ? window.startPicker.selectedDates[0] : null;
                    const endDate = window.endPicker ? window.endPicker.selectedDates[0] : null;

                    if (startDate && endDate) {
                        if (startDate.getTime() === endDate.getTime()) {
                            Swal.fire({
                                text: @json(translate('home.end_date_must_be_after_the_start_date')),
                                icon: 'error'
                            });
                            window.endPicker.clear();
                        } else if (endDate < startDate) {
                            Swal.fire({
                                text: @json(translate('home.end_date_cannot_be_before_start_date')),
                                icon: 'error'
                            });
                            window.endPicker.clear();
                        } else {
                            const daysDifference = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
                            console.log('Valid date range:', {
                                start: startDate.toLocaleDateString('en-CA'),
                                end: endDate.toLocaleDateString('en-CA'),
                                duration: `${daysDifference} day(s)`
                            });
                        }
                    }
                }
            }
    </script>
    <script>
        $(".members_wrapper input[type='time']").each(function() {
            let value = $(this).attr("value");
            let placeholder = $(this).attr("placeholder") || @json(translate('home.select_time'));

            $(this).flatpickr({
                enableTime: true,
                noCalendar: true,
                dateFormat: "h:i K",
                time_24hr: false,
                allowInput: true,
                placeholder: placeholder,
                onReady: function(selectedDates, dateStr, instance) {
                    setTimeout(() => {
                        let hourInput = instance.hourElement;
                        let minuteInput = instance.minuteElement;

                        $(hourInput).on("input", function() {
                            if (this.value.length >= 2) {
                                $(minuteInput).focus(); // Shift focus to minutes field
                            }
                        });

                        $(minuteInput).on("input", function() {
                            if (this.value.length >= 2) {
                                let hour = hourInput.value.padStart(2,
                                    "0"); // Ensure two-digit hour
                                let minute = minuteInput.value.padStart(2,
                                    "0"); // Ensure two-digit minute

                                let meridian = instance.amPM ? instance.amPM
                                    .textContent : "AM"; // Get AM/PM value
                                let formattedTime = `${hour}:${minute} ${meridian}`;

                                instance.setDate(formattedTime,
                                    true); // Update Flatpickr time
                            }
                        });
                    }, 100);
                },
            });
        });

        $(document).on('input', '.drop_off_time, .pick_up_time', function() {
            var pickUp = $('.pick_up_time').val() || "";
            var dropOff = $('.drop_off_time').val() || "";

            $('#guests .number_guests').text(`${pickUp} - ${dropOff}`);
        });

        function validateSearchFields() {
            var lat = $('#lat').val() || '';
            var lng = $('#lng').val() || '';
            var category = $('.top_categories_wrapper [name="category_id"]:checked').val();
            var selectedDate = '';

            if (category == 4) {
                if ($('#dateExp').val() && $('#dateExpEnd').val()) {
                    selectedDate = $('#dateExp').val() + ' : ' + $('#dateExpEnd').val();
                }
            } else {
                selectedDate = $('input.date_ranges').val();
            }

            var people = $('.number_guests').text().trim();
            // var location = $('#locationInput').val() || '';

            var validPeople = people !== '' && people !== 'How many?' && people !== 'Pick-up & Drop-off';

            var isValid = lat !== '' && lng !== '' && selectedDate !== '' && validPeople;

            // console.log('Validation result:', {
            //     lat,
            //     lng,
            //     category,
            //     selectedDate,
            //     people,
            //     validPeople,
            //     isValid
            // });
            

            $('.filter_wrapper .submit input[type="submit"]').prop('disabled', !isValid);
        }

        $(document).on('click','.filter_wrapper .submit:has(input[type="submit"][disabled]) .trans_btn',function(){
            console.log('cliked');
            
            Swal.fire({
                text: @json(translate('home.please_fill_in_all_fields')),
                icon: 'error',
                title: 'Error',
                confirmButtonText: 'OK',
            });
        })



    </script>
@endpush
