<!doctype html>
<html lang="{{ app()->getLocale() }}">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="robots" content="noindex, nofollow, noarchive">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="icon" type="image/x-icon" href="{{ asset('') }}{{ common_setting()->favicon ?? '' }}">
    {{-- <link rel="icon" type="image/x-icon" href="{{ asset('website') }}/images/logo.png"> --}}
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    @php
        $css_cdn_files = [
            'bootstrap.min.css',
            'swiper-bundle.min.css',
            'google-font.css',
            'bootstrap-icons.css',
            'pignose-calendar/css/pignose.calendar.css',
            'select2.min.css',
            'daterangepicker.css',
            'ion.rangeSlider.min.css',
            'fontawesome/css/all.min.css',
        ];
        if (!request()->is('booking-pdf*')) {
            $css_cdn_files[] = 'aos.css';
        }
    @endphp
    @foreach ($css_cdn_files as $css_cdn_file)
        <link href="{{ asset("website/assets_cdn/css/{$css_cdn_file}") }}" rel="stylesheet">
    @endforeach
    {{-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" /> --}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/css/intlTelInput.css">
    <link rel="stylesheet" href="{{ asset('website/css/jquery.toast.css') }}">
    <link rel="stylesheet" href="{{ asset('website') }}/css/style.css">
    <link rel="stylesheet" href="{{ asset('website') }}/css/responsive.css">
    <title class="notranslate">{{ common_setting()->title ?? '' }}</title>
    <script src="{{ asset('website/assets_cdn/js/jquery-3.6.0.js') }}"></script>


    @stack('css')
</head>
<style>
    #sign-up-error ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    #not_verfied {
        background: #8e8e8e;
        color: #ffffffcf;
    }

    .flag_img i {
        font-size: 1.5rem;
        color: #333;
        /* Adjust color if needed */
    }

    /* Custom toast styles */
    .toast {
        min-width: 300px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .toast-header img {
        border: 1px solid #dee2e6;
    }

    .toast-body {
        word-wrap: break-word;
    }
</style>

<body class="body">
    {{-- include translation --}}
    @include('website.layout.translate')
    <div id="sec-loader">
        <div class="load">
            <img src="{{ asset('website') }}/images/luxustar-loader.gif" alt="Loader">
        </div>
    </div>
    @if (!request()->is('booking-pdf*'))
        @include('website.layout.header')
    @endif

    {{-- main container start --}}
    @yield('content')
    {{-- main container end --}}
    @if (!request()->is('booking-pdf*'))
        @include('website.layout.footer')
    @endif

    <!------------------------ Button trigger modal -------------------------------->
    <!-- Change Password Modal -->
    @include('website.layout.changePassword')

    <!-- Experience Modal -->
    @include('website.layout.experience')

    @guest
        {{-- Login Modal --}}
        @include('website.layout.login')

        {{-- Sign Modal --}}
        @include('website.layout.signup')

        {{-- --- Forgot Password Stepper Modal --- --}}
        @include('website.layout.reset')
    @endguest

    {{-- --- Calendar Modal --- --}}
    @include('website.layout.calendar')

    {{-- --- Report Modal --- --}}
    @include('website.layout.report_modal')
    <audio src="{{ asset('website/messenger/notification-audio.wav') }}" id="notification-audio"
        preload="auto"></audio>

    <!-- Toast Container for Message Notifications -->
    <div aria-live="polite" aria-atomic="true" class="position-relative">
        <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
            <!-- Dynamic toasts will be inserted here -->
        </div>
    </div>



    @php
        $js_cdn_files = [
            'jquery.validate.min.js',
            'bootstrap.bundle.min.js',
            'swiper-bundle.min.js',
            'intlTelInput-jquery.min.js',
        ];
        if (!request()->is('booking-pdf*')) {
            $js_cdn_files[] = 'aos.js';
        }
    @endphp
    @foreach ($js_cdn_files as $js_cdn_file)
        <script src="{{ asset("website/assets_cdn/js/{$js_cdn_file}") }}"></script>
    @endforeach
    @if (!in_array('detail', request()->segments()))
        {{-- <script async
            src="https://maps.googleapis.com/maps/api/js?key={{google_map_key()}}&libraries=places&callback=initAutocomplete">
        </script> --}}
        <script src="{{ url('/google-map') }}?libraries=places&callback=initAutocomplete"></script>
    @endif
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


    @include('includes.pusher-notification-toast')

    <script>
        $(function() {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
                }
            });
        });
        var $telephone = $("#telephone");
        $telephone.intlTelInput({
            separateDialCode: true,
        });
        // Event listener for country change
        $telephone.on("countrychange", function() {
            var country_code = $(".iti__selected-dial-code").html();
            $(".country_code").val(country_code);
        });
        $('#autocomplete').on('keyup', function() {
            $(window).keydown(function(event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });
        });
        var placeSearch, autocomplete;

        function initAutocomplete() {
            autocomplete = new google.maps.places.Autocomplete(
                document.getElementById('autocomplete'));
            autocomplete.addListener('place_changed', fillInAddress);
        }

        function fillInAddress() {
            var place = autocomplete.getPlace();
            console.log('Latitude: ' + place.geometry.location.lat());
            console.log('Longitude: ' + place.geometry.location.lng());
            $('#lat').val(place.geometry.location.lat());
            $('#lng').val(place.geometry.location.lng());
        }

        function fillInAddress() {
            var place = autocomplete.getPlace();
            if (!place.geometry) {
                alert("No details available for input: '" + place.name + "'");
                return;
            }
            console.log('Latitude: ' + place.geometry.location.lat());
            console.log('Longitude: ' + place.geometry.location.lng());
            $('#lat').val(place.geometry.location.lat());
            $('#lng').val(place.geometry.location.lng());
        }


        function hasLocationFromURL() {
            // Check if we have lat/lng parameters in URL or if we're on a search page with location
            const urlParams = new URLSearchParams(window.location.search);
            const hasLatLng = urlParams.has('lat') && urlParams.has('lng');
            const isLocationSearch = window.location.pathname.includes('/search/');

            return hasLatLng || isLocationSearch;
        }

        // function getLocation() {
        //     if (navigator.geolocation) {
        //         navigator.geolocation.watchPosition(showPosition);
        //     } else {
        //         alert('Geolocation is not supported by this browser');
        //     }
        // }

        // function showPosition(position) {
        //     if ($("#lat").val() === '' && $("#lng").val() === '') {
        //         $("#lat").val(position.coords.latitude);
        //         $("#lng").val(position.coords.longitude);
        //         $("#autocomplete").val("Current Location");
        //         $("#autocomplete").attr("placeholder", "{{ translate('home.enter_destination') }}");
        //     }
        // }

        // function getLocationFromSession() {
        //     var sessionLat = "{{ session('lat') }}";
        //     var sessionLng = "{{ session('lng') }}";

        //     // Only update if no existing values from input fields
        //     if ($("#lat").val() === '' && $("#lng").val() === '') {
        //         $("#final").val(sessionLng);
        //         $("#lat").val(sessionLat);
        //         $("#lng").val(sessionLng);
        //         $("#autocomplete").attr("placeholder", "{{ translate('home.enter_destination') }}");
        //     }
        // }
        // $(document).ready(function() {
        //     // Only get location if no URL parameters are present
        //     if (!hasLocationFromURL()) {
        //         getLocationFromSession();
        //     }
        // });
        $("#not_verfied").on("click", function() {
            Swal.fire(
                `Verify identity`,
                `Please verify your identity`,
                'error'
            ).then(function() {
                // window.location.href = '{{ url('account-setting') . '#verfication-kyc' }}';
                window.open(
                    "{{ route('webaccount_setting', ['locale' => app()->getLocale()]) }}#verfication-kyc",
                    "_blank")
            })
        })
        $("#search-listing-inp").on("keyup", function() {
            var listing_name = $(this).val();
            if (listing_name != "") {
                $("#listing_name_search").val(listing_name);
                $("#listing-search-dropdown").show()
                let listing_dropdown = $("#listing-search-dropdown ul")
                listing_dropdown.empty();
                listing_dropdown.html(
                    `<div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>`
                );
                $.ajax({
                    url: "{{ route('listing_search') }}",
                    data: {
                        listing_name
                    },
                    success: function(response) {
                        listing_dropdown.empty();
                        if (response.status == true) {
                            $("#listing-search-dropdown ul").append(`${response.data}`)
                        } else {
                            $("#listing-search-dropdown ul").append(`<li>${response.message}</li>`)
                        }
                    },
                    complete: function() {
                        $('.spinner-border').hide();
                    }
                })
            } else {
                $("#listing_name_search").val('');
                $("#listing-search-dropdown").hide()
            }
        })
        $("#close-listing-search").on("click", function() {
            $("#listing-search-dropdown").hide();
            $("#search-listing-inp").val("");
        })
        $(document).on("click", ".disable-on-click", function() {
            var button = $(this);
            button.prop("disabled", true);
            setTimeout(function() {
                button.prop("disabled", false);
            }, 4000);
        });
        setInterval(() => {
            if (localStorage.getItem("user_kyc")) {
                localStorage.removeItem('user_kyc');
                window.location.href =
                    '{{ route('webaccount_setting', ['locale' => app()->getLocale()]) . '#verfication-kyc' }}';
            }
        }, 1000);
    </script>
    <script src="{{ asset('website/js/main.js') }}"></script>
    @if (session()->has('message') || session()->has('flesh_message'))
        <script>
            Swal.fire({
                icon: '{{ session()->get('type') }}',
                title: '{{ session()->get('title') }}',
                text: '{{ session()->get('message') }}',
            })
        </script>
    @endif
    @if (session()->has('login_message'))
        <script>
            $(function() {
                $('#login').modal('show');
                $('#signUp').modal('hide');
            });
        </script>
    @endif
    <script>
        $("body").delegate('#search_currency', "keyup", function() {
            var value = $(this).val().toLowerCase();
            var rowCount = 0;
            $(".currency_parent li").filter(function() {
                var match = $(this).find('.currency_name').text().toLowerCase().indexOf(value) > -1;
                $(this).toggle(match);
                if (match) rowCount++;
                // console.log(rowCount);

                $('.no_found_data').show();
            });

            if (rowCount === 0) {
                if (!$('.currency_parent').find('.no_found_data').length) {
                    $(".currency_parent").append(
                        '<li class="dropdown-item no_found_data lang-pic"><span class="text-center no-data-found">No currency found</span></li>'
                    );
                }
            } else {
                $(".no-data-found").text("");
                $('.no_found_data').remove();
            }
        });
    </script>
    <script>
        var loader = document.getElementById("sec-loader");
        window.addEventListener("load", function() {
            loader.style.display = "none"
        });
    </script>
    <script>
        AOS.init();
    </script>
    @stack('js')
</body>

</html>
